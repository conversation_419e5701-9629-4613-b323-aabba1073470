# FAISS 服务器并发模型配置指南

## 🎯 概述

FAISS 服务器现在支持完全配置化的并发模型，允许你根据具体的负载特征和硬件配置来优化性能。

## 🔧 配置选项

### 基础配置

```bash
# 查看帮助
python3.11 smart_faiss_server.py --help

# 基本启动
python3.11 smart_faiss_server.py --use-gunicorn
```

### 并发模型配置

#### 1. 自动配置模式 (推荐新手)
```bash
python3.11 smart_faiss_server.py --use-gunicorn --auto-config
```
- 根据CPU核数自动选择最佳配置
- 16核以上：8进程×2线程
- 8-15核：4进程×2线程
- 8核以下：单进程×全部线程

#### 2. 预设模式

**平衡模式 (默认)**
```bash
python3.11 smart_faiss_server.py --use-gunicorn --concurrency-model balanced
```
- 适合大多数场景
- 8进程×2线程 = 16总线程

**HTTP优化模式**
```bash
python3.11 smart_faiss_server.py --use-gunicorn --concurrency-model http-optimized
```
- 适合高QPS小批量请求
- 16进程×1线程 = 16总线程

**计算优化模式**
```bash
python3.11 smart_faiss_server.py --use-gunicorn --concurrency-model compute-optimized
```
- 适合大批量搜索
- 4进程×4线程 = 16总线程

#### 3. 自定义模式

**指定进程和线程数**
```bash
python3.11 smart_faiss_server.py --use-gunicorn --workers 4 --omp-threads 4
```

**单进程模式**
```bash
python3.11 smart_faiss_server.py --omp-threads 16
```

**限制总线程数**
```bash
python3.11 smart_faiss_server.py --use-gunicorn --workers 8 --total-threads 12
```

## 🚀 便捷启动脚本

### 使用配置化启动脚本

```bash
# 查看帮助
./start_faiss_configurable.sh help

# 平衡模式 (默认)
./start_faiss_configurable.sh balanced

# HTTP优化模式
./start_faiss_configurable.sh http-optimized

# 计算优化模式
./start_faiss_configurable.sh compute-optimized

# 自动配置
./start_faiss_configurable.sh auto

# 自定义配置
./start_faiss_configurable.sh custom --workers 4 --omp-threads 4
./start_faiss_configurable.sh custom --workers 16 --omp-threads 1
./start_faiss_configurable.sh custom --omp-threads 16  # 单进程
```

## 📊 性能对比和选择指南

### 负载特征分析

| 负载类型 | 批次大小 | QPS需求 | 推荐配置 | 原因 |
|----------|----------|---------|----------|------|
| **高并发小批量** | 1-10个向量 | >2000 | HTTP优化 (16×1) | Python并发处理能力最大化 |
| **中等批量** | 50-200个向量 | 1000-2000 | 平衡模式 (8×2) | 平衡HTTP处理和搜索性能 |
| **大批量搜索** | 500+个向量 | <1000 | 计算优化 (4×4) | 单次搜索性能最大化 |
| **混合负载** | 变化较大 | 中等 | 自动配置 | 根据硬件自适应 |

### 硬件配置建议

| CPU核数 | 内存 | 推荐配置 | 说明 |
|---------|------|----------|------|
| **4-8核** | 8-16GB | 单进程×全部线程 | 避免进程切换开销 |
| **8-16核** | 16-32GB | 平衡模式 (8×2) | 最佳性价比 |
| **16-32核** | 32-64GB | 自定义调优 | 根据具体负载优化 |
| **32+核** | 64GB+ | 分层配置 | 考虑NUMA拓扑 |

## 🔍 性能监控和调优

### 监控指标

```bash
# 查看进程状态
ps aux | grep smart_faiss_server

# 监控CPU使用率
htop

# 查看内存使用
free -h

# 监控网络连接
ss -tuln | grep 8005
```

### 性能测试

```bash
# 基础性能测试
python3.11 -m vectordb_bench.cli.vectordbbench faissremote \
    --uri http://10.1.180.72:8005 \
    --case-type Performance768D1M \
    --index-type HNSW --m 30 --ef-construction 360 --ef-search 100 \
    --concurrency-duration 20 --num-concurrency 4,8,12

# 并发模型对比测试
python3.11 benchmark_concurrency_models.py --quick
```

## 🛠️ 故障排除

### 常见问题

1. **连接重置错误**
   ```bash
   # 检查文件描述符限制
   ulimit -n
   
   # 增加限制
   ulimit -n 65536
   ```

2. **内存不足**
   ```bash
   # 检查内存使用
   free -h
   
   # 减少进程数或线程数
   ./start_faiss_configurable.sh custom --workers 4 --omp-threads 2
   ```

3. **CPU使用率不均**
   ```bash
   # 使用CPU绑定
   numactl -C 0-15 python3.11 smart_faiss_server.py --use-gunicorn
   ```

### 日志分析

```bash
# 查看启动日志
tail -f faiss_*.log

# 搜索错误信息
grep -i error faiss_*.log

# 监控搜索性能
grep "搜索成功" faiss_*.log | tail -20
```

## 📈 最佳实践

### 1. 生产环境配置

```bash
# 推荐的生产环境启动命令
numactl -C 0-15 python3.11 smart_faiss_server.py \
    --use-gunicorn \
    --workers 8 \
    --omp-threads 2 \
    --preload
```

### 2. 开发环境配置

```bash
# 开发环境快速启动
./start_faiss_configurable.sh auto
```

### 3. 压力测试配置

```bash
# 高并发压力测试
./start_faiss_configurable.sh http-optimized
```

## 🔗 相关文档

- [OpenMP 配置详解](https://www.openmp.org/)
- [Gunicorn 配置指南](https://docs.gunicorn.org/)
- [FAISS 性能优化](https://github.com/facebookresearch/faiss/wiki/Guidelines-to-choose-an-index)

---

**配置化并发模型让你能够根据具体需求优化FAISS服务器性能，实现最佳的QPS和延迟表现！** 🚀
