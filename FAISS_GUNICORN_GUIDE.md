# FAISS Gunicorn 多进程部署指南

## 🎯 概述

本指南提供了使用 Gunicorn + preload 模式部署 FAISS 服务器的完整方案，解决了内存爆炸问题，实现真正的多进程高性能部署。

## 🔑 关键改进

### ❌ **之前的问题**
- 单进程模式，无法充分利用 16 核 CPU
- 注释掉的多进程配置缺少 `--preload-app` 参数
- 内存使用效率低，性能受限

### ✅ **现在的解决方案**
- Gunicorn 8 进程 + preload_app = True
- Linux Copy-on-Write 共享 FAISS 索引内存
- 充分利用 16 核 CPU，预期性能提升 3-6 倍

## 📋 文件清单

| 文件名 | 用途 | 说明 |
|--------|------|------|
| `gunicorn_faiss_config.py` | Gunicorn 配置 | 核心配置文件，包含 preload_app=True |
| `start_faiss_with_cgroup.sh` | 服务启动脚本 | 修改后支持 Gunicorn 多进程 |
| `verify_faiss_gunicorn.sh` | 服务验证脚本 | 检查服务状态和性能 |
| `test_faiss_gunicorn_performance.sh` | 性能测试脚本 | 完整的性能对比测试 |
| `stop_faiss_gunicorn.sh` | 服务停止脚本 | 优雅停止所有进程 |

## 🚀 快速启动

### 1. 安装依赖
```bash
pip install gunicorn
```

### 2. 启动服务
```bash
./start_faiss_with_cgroup.sh
```

### 3. 验证服务
```bash
./verify_faiss_gunicorn.sh
```

### 4. 运行性能测试
```bash
./test_faiss_gunicorn_performance.sh
```

### 5. 停止服务
```bash
./stop_faiss_gunicorn.sh
```

## 📊 配置详解

### Gunicorn 关键配置
```python
# gunicorn_faiss_config.py 核心配置
bind = "0.0.0.0:8005"
workers = 8                    # 8个Worker进程
worker_class = "uvicorn.workers.UvicornWorker"
preload_app = True            # 🔑 关键：预加载应用
max_requests = 2000
worker_connections = 1000
```

### 环境变量配置
```bash
# 每个 Worker 使用 2 个 OpenMP 线程
export OMP_NUM_THREADS=2       # 8 workers × 2 threads = 16 total
export MKL_NUM_THREADS=2
export OPENBLAS_NUM_THREADS=2
export DATASET_LOCAL_DIR=/nas/yvan.chen/milvus/dataset
```

## 💾 内存使用对比

### 不使用 preload (内存爆炸)
```
假设 FAISS 索引 = 10GB
8个Worker × 10GB = 80GB 总内存
64GB 服务器 → 💥 OOM 崩溃
```

### 使用 preload (内存共享)
```
主进程预加载 10GB 索引
8个Worker 共享索引内存 (Copy-on-Write)
总内存 ≈ 10GB + 进程开销 ≈ 12-15GB
64GB 服务器 → ✅ 内存充足
```

## 📈 性能预期

| 指标 | 单进程模式 (之前) | 多进程模式 (现在) |
|------|------------------|------------------|
| **QPS** | 470 | 1500-3000 |
| **CPU 利用率** | 25% (4核/16核) | 80-90% (充分利用) |
| **内存使用** | 8-12GB | 12-18GB (共享) |
| **延迟 P99** | 18-36ms | 10-25ms |
| **并发连接** | 200 | 8000 (8×1000) |

## 🔧 监控命令

### 服务器端监控
```bash
# 查看进程
ps aux | grep gunicorn

# 查看内存使用
watch -n 1 'ps aux | grep gunicorn'

# 查看日志
tail -f faiss_server_gunicorn.log

# 检查端口
netstat -tlnp | grep 8005
```

### 客户端测试
```bash
# API 测试
curl http://10.1.180.72:8005/status

# 网络延迟
ping 10.1.180.72

# 性能测试
./test_faiss_gunicorn_performance.sh
```

## 🛠️ 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 检查日志
tail -f faiss_server_gunicorn.log

# 检查端口占用
netstat -tlnp | grep 8005

# 手动启动测试
gunicorn smart_faiss_server:app --config gunicorn_faiss_config.py
```

#### 2. 内存不足
```bash
# 检查内存使用
free -h

# 减少 Worker 数量
# 编辑 gunicorn_faiss_config.py: workers = 4
```

#### 3. 性能不达预期
```bash
# 检查 CPU 使用
htop

# 检查线程配置
echo $OMP_NUM_THREADS

# 调整并发参数
# 编辑配置文件调整 worker_connections
```

## 🎯 测试流程

### 基础功能测试
```bash
# 小数据集快速验证
python3.11 -m vectordb_bench.cli.vectordbbench faissremote \
    --uri http://10.1.180.72:8005 \
    --case-type Performance768D50K \
    --index-type HNSW \
    --m 16 --ef-construction 128 --ef-search 64 \
    --concurrency-duration 10 --num-concurrency 4,8
```

### 性能对比测试
```bash
# 与单进程模式对比
python3.11 -m vectordb_bench.cli.vectordbbench faissremote \
    --uri http://10.1.180.72:8005 \
    --case-type Performance768D1M \
    --index-type HNSW \
    --m 30 --ef-construction 360 --ef-search 100 \
    --concurrency-duration 30 --num-concurrency 8,16,32,64,128
```

### 压力测试
```bash
# 高并发压力测试
python3.11 -m vectordb_bench.cli.vectordbbench faissremote \
    --uri http://10.1.180.72:8005 \
    --case-type Performance768D1M \
    --index-type HNSW \
    --m 30 --ef-construction 360 --ef-search 100 \
    --concurrency-duration 60 --num-concurrency 64,128,256
```

## 💡 最佳实践

### 1. 生产环境配置
- 使用专用的配置文件
- 设置适当的日志级别
- 配置监控和告警
- 定期重启 Worker (max_requests)

### 2. 性能调优
- 根据 CPU 核心数调整 Worker 数量
- 根据内存大小调整 worker_connections
- 监控并调整 OpenMP 线程数
- 使用 SSD 存储提高 I/O 性能

### 3. 安全考虑
- 配置防火墙规则
- 使用反向代理 (Nginx)
- 设置资源限制 (Cgroup)
- 定期更新依赖包

## 🎉 总结

通过正确配置 Gunicorn + preload_app，我们解决了 FAISS 服务器的内存爆炸问题，实现了真正的多进程高性能部署。预期性能提升 3-6 倍，充分利用 16 核 CPU 资源。

关键成功因素：
1. ✅ `preload_app = True` - 避免内存重复加载
2. ✅ 合理的 Worker 数量 - 匹配 CPU 核心数
3. ✅ 正确的线程配置 - 总线程数控制
4. ✅ 完善的监控和测试 - 确保稳定运行
