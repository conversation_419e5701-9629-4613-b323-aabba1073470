#!/usr/bin/env python3
"""
调试并发参数传递问题
"""

import sys
import os
sys.path.append('/home/<USER>/VectorDBBench')

def debug_cli_parsing():
    """调试CLI参数解析"""
    print("🔍 调试CLI参数解析")
    print("=" * 50)
    
    # 模拟命令行参数
    test_args = [
        "faissremote",
        "--uri", "http://***********:8005",
        "--case-type", "Performance768D10M",
        "--index-type", "HNSW",
        "--m", "30",
        "--ef-construction", "360",
        "--concurrency-duration", "30",
        "--num-concurrency", "128,256,512"
    ]
    
    print(f"📊 测试参数: {test_args}")
    
    # 测试click参数解析
    try:
        from vectordb_bench.cli.cli import click_arg_split
        
        # 测试并发参数解析
        concurrency_str = "128,256,512"
        parsed = click_arg_split(None, None, concurrency_str)
        int_parsed = list(map(int, parsed))
        
        print(f"✅ 并发参数解析成功:")
        print(f"   原始字符串: {concurrency_str}")
        print(f"   解析结果: {parsed}")
        print(f"   整数转换: {int_parsed}")
        
    except Exception as e:
        print(f"❌ 并发参数解析失败: {e}")
        import traceback
        traceback.print_exc()

def debug_config_loading():
    """调试配置加载"""
    print("\n🔍 调试配置加载")
    print("=" * 50)
    
    try:
        from vectordb_bench import config
        
        print(f"📊 默认配置:")
        print(f"   NUM_CONCURRENCY: {config.NUM_CONCURRENCY}")
        print(f"   CONCURRENCY_DURATION: {config.CONCURRENCY_DURATION}")
        print(f"   CONCURRENCY_TIMEOUT: {config.CONCURRENCY_TIMEOUT}")
        
        # 检查环境变量
        import environs
        env = environs.Env()
        
        num_concurrency_env = env.list("NUM_CONCURRENCY", None, subcast=int)
        if num_concurrency_env:
            print(f"⚠️  环境变量覆盖: NUM_CONCURRENCY={num_concurrency_env}")
        else:
            print(f"✅ 无环境变量覆盖")
            
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        import traceback
        traceback.print_exc()

def debug_case_config():
    """调试Case配置"""
    print("\n🔍 调试Case配置")
    print("=" * 50)
    
    try:
        from vectordb_bench.backend.cases import Performance768D10M
        from vectordb_bench.models import ConcurrencySearchConfig
        
        # 检查Case配置
        case = Performance768D10M()
        print(f"📊 Performance768D10M配置:")
        print(f"   case_id: {case.case_id}")
        print(f"   name: {case.name}")
        print(f"   dataset: {case.dataset}")
        
        # 检查并发搜索配置
        concurrency_config = ConcurrencySearchConfig()
        print(f"📊 并发搜索配置:")
        print(f"   num_concurrency: {concurrency_config.num_concurrency}")
        print(f"   concurrency_duration: {concurrency_config.concurrency_duration}")
        print(f"   concurrency_timeout: {concurrency_config.concurrency_timeout}")
        
    except Exception as e:
        print(f"❌ Case配置检查失败: {e}")
        import traceback
        traceback.print_exc()

def debug_task_runner():
    """调试TaskRunner"""
    print("\n🔍 调试TaskRunner")
    print("=" * 50)
    
    try:
        # 模拟创建TaskRunner
        from vectordb_bench.backend.task_runner import TaskRunner
        from vectordb_bench.backend.clients.faiss.config import FaissConfig, FaissDBCaseConfig
        from vectordb_bench.backend.clients import DB
        from vectordb_bench.backend.cases import Performance768D10M
        
        # 创建配置
        db_config = FaissConfig(
            host="***********",
            port=8005,
            index_type="HNSW"
        )
        
        db_case_config = FaissDBCaseConfig(
            m=30,
            ef_construction=360,
            ef_search=100
        )
        
        case_config = Performance768D10M()
        
        # 模拟参数
        test_params = {
            'num_concurrency': [128, 256, 512],  # 这是我们期望的
            'concurrency_duration': 30,
            'concurrency_timeout': 3600,
            'k': 100,
            'search_concurrent': True
        }
        
        print(f"📊 测试参数:")
        for key, value in test_params.items():
            print(f"   {key}: {value}")
        
        # 检查TaskRunner是否会覆盖参数
        print(f"\n🔍 检查参数传递...")
        
        # 这里我们不实际创建TaskRunner，只是检查配置
        print(f"✅ 参数检查完成")
        
    except Exception as e:
        print(f"❌ TaskRunner检查失败: {e}")
        import traceback
        traceback.print_exc()

def create_test_command():
    """创建测试命令"""
    print("\n🧪 创建测试命令")
    print("=" * 50)
    
    # 创建一个简单的测试脚本
    test_script = '''#!/bin/bash
echo "🧪 测试VectorDBBench并发参数传递"
echo "命令行参数: $@"

# 设置环境变量来追踪参数
export PYTHONPATH="/home/<USER>/VectorDBBench:$PYTHONPATH"

# 运行带调试的VectorDBBench
python3 -c "
import sys
print('Python参数:', sys.argv)
sys.path.append('/home/<USER>/VectorDBBench')

# 导入并检查配置
from vectordb_bench import config
print('默认并发配置:', config.NUM_CONCURRENCY)

# 模拟CLI调用
from vectordb_bench.cli.cli import click_arg_split
concurrency_str = '128,256,512'
parsed = click_arg_split(None, None, concurrency_str)
int_parsed = list(map(int, parsed))
print('解析的并发参数:', int_parsed)
" "$@"
'''
    
    with open("test_concurrency_debug.sh", "w") as f:
        f.write(test_script)
    
    os.chmod("test_concurrency_debug.sh", 0o755)
    print("✅ 创建测试脚本: test_concurrency_debug.sh")
    
    print("\n🚀 运行测试:")
    print("./test_concurrency_debug.sh --num-concurrency 128,256,512")

def main():
    """主函数"""
    print("🔬 VectorDBBench 并发参数调试")
    print("=" * 60)
    
    # 1. 调试CLI参数解析
    debug_cli_parsing()
    
    # 2. 调试配置加载
    debug_config_loading()
    
    # 3. 调试Case配置
    debug_case_config()
    
    # 4. 调试TaskRunner
    debug_task_runner()
    
    # 5. 创建测试命令
    create_test_command()
    
    print("\n" + "=" * 60)
    print("🎯 调试完成")
    print("\n💡 可能的问题:")
    print("1. 环境变量 NUM_CONCURRENCY 覆盖了命令行参数")
    print("2. Case配置中硬编码了并发列表")
    print("3. TaskRunner中有参数过滤逻辑")
    print("4. CLI参数解析有问题")
    
    print("\n🔧 建议解决方案:")
    print("1. 检查环境变量: env | grep NUM_CONCURRENCY")
    print("2. 使用 --dry-run 查看实际配置")
    print("3. 直接修改默认配置文件")

if __name__ == "__main__":
    main()
