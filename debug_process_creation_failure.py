#!/usr/bin/env python3
"""
深度诊断进程创建失败的具体原因
"""

import os
import sys
import time
import multiprocessing as mp
import subprocess
import signal
import logging
from concurrent.futures import ProcessPoolExecutor, as_completed

# 设置环境变量
os.environ['DATASET_LOCAL_DIR'] = '/nas/yvan.chen/milvus/dataset'

# 设置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s | %(levelname)s: %(message)s')
log = logging.getLogger(__name__)

class ProcessCreationDebugger:
    def __init__(self):
        self.max_successful_processes = 0
        
    def test_basic_process_creation(self):
        """测试基础进程创建"""
        print("🔍 基础进程创建测试")
        print("=" * 50)
        
        def simple_worker(worker_id):
            """最简单的工作函数"""
            import os
            import time
            pid = os.getpid()
            time.sleep(0.1)  # 短暂工作
            return f"worker_{worker_id}_pid_{pid}_done"
        
        # 逐步增加进程数
        test_levels = [1, 2, 4, 8, 16, 32, 48, 64, 80, 96, 112, 128]
        
        for level in test_levels:
            print(f"\n📊 测试 {level} 个进程...")
            
            try:
                start_time = time.time()
                
                with ProcessPoolExecutor(
                    max_workers=level,
                    mp_context=mp.get_context("spawn")
                ) as executor:
                    
                    # 提交所有任务
                    futures = [executor.submit(simple_worker, i) for i in range(level)]
                    
                    # 等待所有任务完成
                    completed = 0
                    failed = 0
                    results = []
                    
                    for future in as_completed(futures, timeout=30):
                        try:
                            result = future.result()
                            results.append(result)
                            completed += 1
                        except Exception as e:
                            failed += 1
                            log.error(f"任务失败: {e}")
                    
                    total_time = time.time() - start_time
                    
                    print(f"   完成: {completed}/{level}")
                    print(f"   失败: {failed}")
                    print(f"   时间: {total_time:.2f}s")
                    
                    if completed == level:
                        print(f"   ✅ {level} 进程全部成功")
                        self.max_successful_processes = level
                    else:
                        print(f"   ❌ {level} 进程部分失败")
                        return level
                        
            except Exception as e:
                print(f"   ❌ {level} 进程创建异常: {e}")
                return level
        
        print(f"\n✅ 最大成功进程数: {self.max_successful_processes}")
        return None
    
    def test_spawn_vs_fork(self):
        """测试不同的进程启动方法"""
        print("\n🔍 测试不同进程启动方法")
        print("=" * 50)
        
        def worker(worker_id):
            return f"worker_{worker_id}_done"
        
        methods = ["spawn", "fork", "forkserver"]
        test_size = 32
        
        for method in methods:
            print(f"\n📊 测试方法: {method}")
            
            try:
                if method == "fork" and not hasattr(os, 'fork'):
                    print(f"   ❌ {method} 不支持")
                    continue
                
                start_time = time.time()
                
                with ProcessPoolExecutor(
                    max_workers=test_size,
                    mp_context=mp.get_context(method)
                ) as executor:
                    
                    futures = [executor.submit(worker, i) for i in range(test_size)]
                    
                    completed = 0
                    for future in as_completed(futures, timeout=20):
                        try:
                            future.result()
                            completed += 1
                        except:
                            pass
                    
                    total_time = time.time() - start_time
                    
                    print(f"   完成: {completed}/{test_size}")
                    print(f"   时间: {total_time:.2f}s")
                    
                    if completed == test_size:
                        print(f"   ✅ {method} 方法成功")
                    else:
                        print(f"   ❌ {method} 方法失败")
                        
            except Exception as e:
                print(f"   ❌ {method} 方法异常: {e}")
    
    def test_system_limits(self):
        """测试系统限制"""
        print("\n🔍 系统限制检查")
        print("=" * 50)
        
        # 检查各种系统限制
        limits = {}
        
        try:
            # 进程限制
            result = subprocess.run(['bash', '-c', 'ulimit -u'], capture_output=True, text=True)
            limits['max_processes'] = result.stdout.strip()
            
            # 文件描述符限制
            result = subprocess.run(['bash', '-c', 'ulimit -n'], capture_output=True, text=True)
            limits['max_files'] = result.stdout.strip()
            
            # 内存限制
            result = subprocess.run(['bash', '-c', 'ulimit -v'], capture_output=True, text=True)
            limits['max_memory'] = result.stdout.strip()
            
            # 栈大小
            result = subprocess.run(['bash', '-c', 'ulimit -s'], capture_output=True, text=True)
            limits['stack_size'] = result.stdout.strip()
            
            print(f"📊 系统限制:")
            for key, value in limits.items():
                print(f"   {key}: {value}")
                
        except Exception as e:
            print(f"❌ 无法获取系统限制: {e}")
        
        # 检查当前进程状态
        try:
            import psutil
            current_process = psutil.Process()
            print(f"\n📊 当前进程状态:")
            print(f"   PID: {current_process.pid}")
            print(f"   内存使用: {current_process.memory_info().rss // (1024*1024)} MB")
            print(f"   CPU使用: {current_process.cpu_percent()}%")
            print(f"   线程数: {current_process.num_threads()}")
            print(f"   文件描述符: {current_process.num_fds()}")
            
        except Exception as e:
            print(f"❌ 无法获取进程状态: {e}")
    
    def test_multiprocessing_context(self):
        """测试multiprocessing上下文问题"""
        print("\n🔍 multiprocessing上下文测试")
        print("=" * 50)
        
        def context_worker(worker_id, context_name):
            """测试上下文的工作函数"""
            import os
            import multiprocessing as mp
            return {
                'worker_id': worker_id,
                'context': context_name,
                'pid': os.getpid(),
                'mp_current_process': mp.current_process().name
            }
        
        contexts = ["spawn", "fork"] if hasattr(os, 'fork') else ["spawn"]
        test_size = 16
        
        for context_name in contexts:
            print(f"\n📊 测试上下文: {context_name}")
            
            try:
                ctx = mp.get_context(context_name)
                
                with ProcessPoolExecutor(
                    max_workers=test_size,
                    mp_context=ctx
                ) as executor:
                    
                    futures = [
                        executor.submit(context_worker, i, context_name) 
                        for i in range(test_size)
                    ]
                    
                    results = []
                    for future in as_completed(futures, timeout=15):
                        try:
                            result = future.result()
                            results.append(result)
                        except Exception as e:
                            print(f"   任务异常: {e}")
                    
                    print(f"   成功: {len(results)}/{test_size}")
                    if results:
                        print(f"   示例结果: {results[0]}")
                        
            except Exception as e:
                print(f"   ❌ 上下文 {context_name} 失败: {e}")
    
    def test_memory_pressure(self):
        """测试内存压力下的进程创建"""
        print("\n🔍 内存压力测试")
        print("=" * 50)
        
        def memory_worker(worker_id, memory_mb):
            """消耗指定内存的工作函数"""
            import time
            # 分配内存
            data = bytearray(memory_mb * 1024 * 1024)
            time.sleep(0.1)
            return f"worker_{worker_id}_memory_{memory_mb}MB"
        
        # 测试不同内存压力下的进程创建
        memory_levels = [10, 50, 100]  # MB per process
        process_count = 32
        
        for memory_mb in memory_levels:
            print(f"\n📊 测试每进程 {memory_mb}MB 内存...")
            
            try:
                start_time = time.time()
                
                with ProcessPoolExecutor(
                    max_workers=process_count,
                    mp_context=mp.get_context("spawn")
                ) as executor:
                    
                    futures = [
                        executor.submit(memory_worker, i, memory_mb) 
                        for i in range(process_count)
                    ]
                    
                    completed = 0
                    for future in as_completed(futures, timeout=30):
                        try:
                            future.result()
                            completed += 1
                        except:
                            pass
                    
                    total_time = time.time() - start_time
                    total_memory = memory_mb * process_count
                    
                    print(f"   完成: {completed}/{process_count}")
                    print(f"   总内存: {total_memory}MB")
                    print(f"   时间: {total_time:.2f}s")
                    
            except Exception as e:
                print(f"   ❌ 内存测试失败: {e}")
    
    def run_comprehensive_debug(self):
        """运行全面的调试"""
        print("🔬 进程创建失败深度调试")
        print("=" * 60)
        
        # 1. 系统限制检查
        self.test_system_limits()
        
        # 2. 基础进程创建测试
        failure_point = self.test_basic_process_creation()
        
        # 3. 不同启动方法测试
        self.test_spawn_vs_fork()
        
        # 4. multiprocessing上下文测试
        self.test_multiprocessing_context()
        
        # 5. 内存压力测试
        self.test_memory_pressure()
        
        # 6. 生成报告
        print(f"\n" + "=" * 60)
        print(f"📋 调试报告")
        print(f"=" * 60)
        
        if failure_point:
            print(f"❌ 进程创建在 {failure_point} 个进程时失败")
            print(f"💡 最大安全并发数: {self.max_successful_processes}")
        else:
            print(f"✅ 进程创建测试全部通过")
            print(f"💡 最大测试并发数: {self.max_successful_processes}")
        
        print(f"\n🔧 建议解决方案:")
        if failure_point and failure_point < 64:
            print(f"1. 系统可能有进程创建限制")
            print(f"2. 检查 ulimit 设置")
            print(f"3. 检查系统负载和资源使用")
            print(f"4. 考虑使用更轻量的并发模式")
        
        print(f"5. 对于VectorDBBench，建议并发数不超过 {max(self.max_successful_processes, 32)}")

def main():
    debugger = ProcessCreationDebugger()
    debugger.run_comprehensive_debug()

if __name__ == "__main__":
    main()
