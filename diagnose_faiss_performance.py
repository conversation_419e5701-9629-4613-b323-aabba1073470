#!/usr/bin/env python3
"""
诊断FAISS服务端性能问题
"""

import asyncio
import aiohttp
import time
import json
import numpy as np
from typing import List, Dict, Any

class FAISSPerformanceDiagnostic:
    def __init__(self, endpoint: str = "http://10.1.180.72:8005"):
        self.base_url = endpoint
        self.endpoint = f"{endpoint}/search"
        self.status_endpoint = f"{endpoint}/status"
        
    async def check_server_status(self):
        """检查服务器状态"""
        print("🔍 检查FAISS服务器状态")
        print("=" * 50)
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(self.status_endpoint) as response:
                    if response.status == 200:
                        status = await response.json()
                        print(f"✅ 服务器状态: {json.dumps(status, indent=2)}")
                        return status
                    else:
                        print(f"❌ 服务器状态异常: HTTP {response.status}")
                        return None
        except Exception as e:
            print(f"❌ 无法连接服务器: {e}")
            return None
    
    async def test_single_request_latency(self):
        """测试单个请求延迟"""
        print("\n🔍 测试单个请求延迟")
        print("=" * 50)
        
        query_vector = np.random.random(768).astype(np.float32).tolist()
        payload = {"query": query_vector, "topk": 100}
        
        latencies = []
        
        async with aiohttp.ClientSession() as session:
            for i in range(10):
                try:
                    start_time = time.perf_counter()
                    async with session.post(self.endpoint, json=payload) as response:
                        if response.status == 200:
                            await response.read()
                            latency_ms = (time.perf_counter() - start_time) * 1000
                            latencies.append(latency_ms)
                            print(f"  请求 {i+1}: {latency_ms:.2f} ms")
                        else:
                            print(f"  请求 {i+1}: HTTP {response.status}")
                except Exception as e:
                    print(f"  请求 {i+1}: 错误 - {e}")
        
        if latencies:
            avg_latency = sum(latencies) / len(latencies)
            min_latency = min(latencies)
            max_latency = max(latencies)
            print(f"\n📊 单请求延迟统计:")
            print(f"   平均: {avg_latency:.2f} ms")
            print(f"   最小: {min_latency:.2f} ms")
            print(f"   最大: {max_latency:.2f} ms")
            return avg_latency
        return None
    
    async def test_progressive_concurrency(self):
        """渐进式并发测试"""
        print("\n🔍 渐进式并发测试")
        print("=" * 50)
        
        concurrency_levels = [1, 4, 8, 16, 32, 64, 128, 256]
        results = []
        
        for concurrency in concurrency_levels:
            print(f"\n📊 测试并发度: {concurrency}")
            
            qps, avg_latency = await self.test_concurrency_level(concurrency, duration=10)
            results.append({
                'concurrency': concurrency,
                'qps': qps,
                'avg_latency': avg_latency
            })
            
            print(f"   QPS: {qps:.1f}")
            print(f"   平均延迟: {avg_latency:.2f} ms")
            
            # 如果延迟过高，停止测试
            if avg_latency > 1000:
                print("   ⚠️ 延迟过高，停止测试")
                break
        
        return results
    
    async def test_concurrency_level(self, concurrency: int, duration: int = 10):
        """测试特定并发级别"""
        request_count = 0
        total_latency = 0
        
        async def worker(session, semaphore):
            nonlocal request_count, total_latency
            
            query_vector = np.random.random(768).astype(np.float32).tolist()
            payload = {"query": query_vector, "topk": 100}
            
            start_time = time.perf_counter()
            end_time = start_time + duration
            
            while time.perf_counter() < end_time:
                try:
                    async with semaphore:
                        req_start = time.perf_counter()
                        async with session.post(self.endpoint, json=payload) as response:
                            if response.status == 200:
                                await response.read()
                                latency = (time.perf_counter() - req_start) * 1000
                                total_latency += latency
                                request_count += 1
                except Exception:
                    pass
        
        # 创建连接器
        connector = aiohttp.TCPConnector(
            limit=concurrency * 2,
            limit_per_host=concurrency * 2,
            keepalive_timeout=30
        )
        
        async with aiohttp.ClientSession(connector=connector) as session:
            semaphore = asyncio.Semaphore(concurrency)
            
            # 启动工作协程
            workers = [
                asyncio.create_task(worker(session, semaphore))
                for _ in range(concurrency)
            ]
            
            # 等待测试完成
            await asyncio.gather(*workers, return_exceptions=True)
        
        # 计算结果
        qps = request_count / duration if duration > 0 else 0
        avg_latency = total_latency / request_count if request_count > 0 else 0
        
        return qps, avg_latency
    
    async def compare_with_baseline(self):
        """与基线性能对比"""
        print("\n🔍 与基线性能对比")
        print("=" * 50)
        
        # 期望的基线性能（基于之前的VectorDBBench结果）
        baseline = {
            128: {'qps': 4912, 'latency': 26},
            256: {'qps': 5026, 'latency': 51}
        }
        
        for concurrency in [128, 256]:
            print(f"\n📊 并发度 {concurrency} 对比:")
            
            current_qps, current_latency = await self.test_concurrency_level(concurrency, duration=15)
            baseline_qps = baseline[concurrency]['qps']
            baseline_latency = baseline[concurrency]['latency']
            
            qps_ratio = current_qps / baseline_qps if baseline_qps > 0 else 0
            latency_ratio = current_latency / baseline_latency if baseline_latency > 0 else 0
            
            print(f"   当前 QPS: {current_qps:.1f}")
            print(f"   基线 QPS: {baseline_qps}")
            print(f"   QPS 比率: {qps_ratio:.2f}x")
            
            print(f"   当前延迟: {current_latency:.2f} ms")
            print(f"   基线延迟: {baseline_latency} ms")
            print(f"   延迟比率: {latency_ratio:.2f}x")
            
            if qps_ratio < 0.5:
                print("   🔴 性能显著下降!")
            elif qps_ratio < 0.8:
                print("   🟡 性能有所下降")
            else:
                print("   🟢 性能正常")
    
    def analyze_performance_degradation(self, results: List[Dict]):
        """分析性能下降原因"""
        print("\n🔍 性能下降原因分析")
        print("=" * 50)
        
        # 查找性能拐点
        for i, result in enumerate(results):
            concurrency = result['concurrency']
            qps = result['qps']
            latency = result['avg_latency']
            
            print(f"并发 {concurrency:3d}: QPS {qps:6.1f}, 延迟 {latency:6.2f} ms")
            
            # 分析性能特征
            if i > 0:
                prev_result = results[i-1]
                qps_change = (qps - prev_result['qps']) / prev_result['qps'] * 100
                latency_change = (latency - prev_result['avg_latency']) / prev_result['avg_latency'] * 100
                
                if qps_change < -10:
                    print(f"         ⚠️ QPS下降 {abs(qps_change):.1f}%")
                if latency_change > 50:
                    print(f"         ⚠️ 延迟增加 {latency_change:.1f}%")
        
        print(f"\n💡 可能的原因:")
        print(f"1. 服务器配置变化 (进程数/线程数)")
        print(f"2. 索引类型或参数变化")
        print(f"3. 数据集大小变化")
        print(f"4. 系统资源限制 (CPU/内存)")
        print(f"5. 网络配置变化")
    
    async def run_full_diagnostic(self):
        """运行完整诊断"""
        print("🔬 FAISS性能诊断")
        print("=" * 60)
        
        # 1. 检查服务器状态
        status = await self.check_server_status()
        
        # 2. 测试单请求延迟
        single_latency = await self.test_single_request_latency()
        
        # 3. 渐进式并发测试
        results = await self.test_progressive_concurrency()
        
        # 4. 与基线对比
        await self.compare_with_baseline()
        
        # 5. 分析性能下降
        if results:
            self.analyze_performance_degradation(results)
        
        print("\n" + "=" * 60)
        print("🎯 诊断完成")

async def main():
    diagnostic = FAISSPerformanceDiagnostic()
    await diagnostic.run_full_diagnostic()

if __name__ == "__main__":
    asyncio.run(main())
