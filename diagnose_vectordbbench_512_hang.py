#!/usr/bin/env python3
"""
专门诊断VectorDBBench 512并发卡住的具体原因
"""

import os
import sys
import time
import psutil
import subprocess
import multiprocessing as mp
import threading
import signal
import logging
from concurrent.futures import ProcessPoolExecutor, as_completed
import requests
import numpy as np

# 设置环境变量
os.environ['DATASET_LOCAL_DIR'] = '/nas/yvan.chen/milvus/dataset'

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)s: %(message)s')
log = logging.getLogger(__name__)

class VectorDBBench512Diagnostic:
    def __init__(self):
        self.endpoint = "http://***********:8005/search"
        self.status_endpoint = "http://***********:8005/status"
        
    def check_prerequisites(self):
        """检查前置条件"""
        print("🔍 检查前置条件")
        print("=" * 50)
        
        # 1. 检查数据集路径
        dataset_dir = os.environ.get('DATASET_LOCAL_DIR')
        print(f"📂 数据集路径: {dataset_dir}")
        
        if dataset_dir and os.path.exists(dataset_dir):
            print(f"✅ 数据集目录存在")
            
            # 检查cohere数据集
            cohere_dir = os.path.join(dataset_dir, 'cohere', 'cohere_large_10m')
            if os.path.exists(cohere_dir):
                files = os.listdir(cohere_dir)
                print(f"✅ Cohere 10M数据集存在，文件数: {len(files)}")
            else:
                print(f"❌ Cohere 10M数据集不存在: {cohere_dir}")
        else:
            print(f"❌ 数据集目录不存在: {dataset_dir}")
        
        # 2. 检查FAISS服务器
        try:
            response = requests.get(self.status_endpoint, timeout=5)
            if response.status_code == 200:
                status = response.json()
                print(f"✅ FAISS服务器正常: {status}")
            else:
                print(f"❌ FAISS服务器异常: {response.status_code}")
        except Exception as e:
            print(f"❌ FAISS服务器连接失败: {e}")
        
        # 3. 检查系统资源
        memory = psutil.virtual_memory()
        print(f"📊 系统资源:")
        print(f"   可用内存: {memory.available // (1024**3)} GB")
        print(f"   CPU核心数: {psutil.cpu_count()}")
        print(f"   当前进程数: {len(psutil.pids())}")
    
    def test_process_creation_stages(self):
        """分阶段测试进程创建，找出卡住的具体阶段"""
        print("\n🔍 分阶段进程创建测试")
        print("=" * 50)
        
        def simple_worker(worker_id):
            """最简单的工作进程"""
            return f"worker_{worker_id}_done"
        
        # 测试不同阶段
        stages = [64, 128, 256, 384, 448, 480, 496, 504, 508, 510, 511, 512]
        
        for stage in stages:
            print(f"\n📊 测试 {stage} 个进程...")
            
            start_time = time.time()
            success = False
            
            try:
                with ProcessPoolExecutor(
                    max_workers=stage,
                    mp_context=mp.get_context("spawn")
                ) as executor:
                    
                    # 提交任务
                    submit_start = time.time()
                    futures = [executor.submit(simple_worker, i) for i in range(stage)]
                    submit_time = time.time() - submit_start
                    
                    print(f"   任务提交时间: {submit_time:.2f}s")
                    
                    # 等待少量任务完成
                    completed = 0
                    for i, future in enumerate(futures):
                        try:
                            result = future.result(timeout=0.5)
                            completed += 1
                        except:
                            break
                        if i >= 4:  # 只等待前5个任务
                            break
                    
                    total_time = time.time() - start_time
                    print(f"   总时间: {total_time:.2f}s")
                    print(f"   完成任务: {completed}/5")
                    
                    if total_time < 30 and completed >= 3:
                        print(f"   ✅ {stage} 进程测试成功")
                        success = True
                    else:
                        print(f"   ❌ {stage} 进程测试失败或超时")
                        return stage
                        
            except Exception as e:
                print(f"   ❌ {stage} 进程创建异常: {e}")
                return stage
        
        print(f"✅ 所有阶段的进程创建都成功")
        return None
    
    def test_multiprocessing_manager_with_512(self):
        """专门测试512进程使用multiprocessing.Manager的情况"""
        print("\n🔍 测试512进程 + multiprocessing.Manager")
        print("=" * 50)
        
        def worker_with_queue(q, cond, worker_id):
            """使用队列和条件变量的工作进程"""
            try:
                # 1. 加入队列
                q.put(worker_id)
                
                # 2. 等待条件变量
                with cond:
                    cond.wait(timeout=30)  # 30秒超时
                
                return f"worker_{worker_id}_completed"
            except Exception as e:
                return f"worker_{worker_id}_error_{e}"
        
        print(f"🚀 启动512进程 + Manager测试...")
        
        try:
            start_time = time.time()
            
            # 创建Manager
            manager_start = time.time()
            with mp.Manager() as manager:
                manager_time = time.time() - manager_start
                print(f"✅ Manager创建时间: {manager_time:.2f}s")
                
                q = manager.Queue()
                cond = manager.Condition()
                
                # 创建进程池
                pool_start = time.time()
                with ProcessPoolExecutor(
                    max_workers=512,
                    mp_context=mp.get_context("spawn")
                ) as executor:
                    pool_time = time.time() - pool_start
                    print(f"✅ 进程池创建时间: {pool_time:.2f}s")
                    
                    # 提交任务
                    submit_start = time.time()
                    futures = [
                        executor.submit(worker_with_queue, q, cond, i)
                        for i in range(512)
                    ]
                    submit_time = time.time() - submit_start
                    print(f"✅ 任务提交时间: {submit_time:.2f}s")
                    
                    # 等待队列填充
                    print(f"📊 等待队列填充...")
                    queue_start = time.time()
                    last_size = 0
                    stall_count = 0
                    
                    while q.qsize() < 512:
                        current_size = q.qsize()
                        elapsed = time.time() - queue_start
                        
                        # 检查是否停滞
                        if current_size == last_size:
                            stall_count += 1
                        else:
                            stall_count = 0
                            last_size = current_size
                        
                        # 每5秒报告进度
                        if int(elapsed) % 5 == 0 and elapsed > 0:
                            progress = (current_size / 512) * 100
                            print(f"   队列进度: {current_size}/512 ({progress:.1f}%), 已等待: {elapsed:.1f}s")
                        
                        # 如果停滞超过30秒，认为卡住了
                        if stall_count > 300:  # 30秒 (每0.1秒检查一次)
                            print(f"   ❌ 队列填充停滞，当前大小: {current_size}/512")
                            return f"queue_stalled_at_{current_size}"
                        
                        # 总超时120秒
                        if elapsed > 120:
                            print(f"   ❌ 队列填充超时，当前大小: {current_size}/512")
                            return f"queue_timeout_at_{current_size}"
                        
                        time.sleep(0.1)
                    
                    queue_time = time.time() - queue_start
                    print(f"✅ 队列填充完成: {queue_time:.2f}s")
                    
                    # 发送同步信号
                    print(f"📊 发送同步信号...")
                    with cond:
                        cond.notify_all()
                    
                    # 等待部分任务完成
                    print(f"📊 等待任务完成...")
                    completed = 0
                    for i, future in enumerate(futures):
                        try:
                            result = future.result(timeout=0.1)
                            if "completed" in str(result):
                                completed += 1
                        except:
                            pass
                        if i >= 9:  # 只检查前10个
                            break
                    
                    total_time = time.time() - start_time
                    print(f"✅ 测试完成:")
                    print(f"   总时间: {total_time:.2f}s")
                    print(f"   完成任务: {completed}/10")
                    
                    return "success"
                    
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
            return f"exception: {e}"
    
    def test_vectordbbench_specific_bottleneck(self):
        """测试VectorDBBench特定的瓶颈"""
        print("\n🔍 VectorDBBench特定瓶颈测试")
        print("=" * 50)
        
        # 模拟VectorDBBench的实际工作流程
        def mock_vectordb_worker(test_data, q, cond, worker_id):
            """模拟VectorDBBench的实际工作进程"""
            try:
                # 1. 进程同步阶段
                q.put(1)
                
                # 2. 等待同步信号
                with cond:
                    cond.wait(timeout=60)
                
                # 3. 模拟VectorDB初始化
                import requests
                session = requests.Session()
                
                # 4. 执行少量搜索测试
                for i in range(2):
                    try:
                        response = session.post(
                            self.endpoint,
                            json={"query": test_data, "topk": 10},
                            timeout=10
                        )
                        if response.status_code != 200:
                            return f"worker_{worker_id}_http_error_{response.status_code}"
                    except Exception as e:
                        return f"worker_{worker_id}_request_error_{e}"
                
                return f"worker_{worker_id}_success"
                
            except Exception as e:
                return f"worker_{worker_id}_exception_{e}"
        
        # 准备测试数据
        test_data = np.random.random(768).tolist()
        
        print(f"🚀 启动VectorDBBench风格的512并发测试...")
        
        try:
            start_time = time.time()
            
            with mp.Manager() as manager:
                q = manager.Queue()
                cond = manager.Condition()
                
                with ProcessPoolExecutor(
                    max_workers=512,
                    mp_context=mp.get_context("spawn")
                ) as executor:
                    
                    # 提交任务
                    futures = [
                        executor.submit(mock_vectordb_worker, test_data, q, cond, i)
                        for i in range(512)
                    ]
                    
                    # 等待队列填充（这是最可能卡住的地方）
                    print(f"📊 等待512个进程同步...")
                    queue_start = time.time()
                    
                    while q.qsize() < 512:
                        current_size = q.qsize()
                        elapsed = time.time() - queue_start
                        
                        if int(elapsed) % 10 == 0 and elapsed > 0:
                            progress = (current_size / 512) * 100
                            print(f"   同步进度: {current_size}/512 ({progress:.1f}%), 已等待: {elapsed:.1f}s")
                        
                        if elapsed > 180:  # 3分钟超时
                            print(f"   ❌ 进程同步超时，当前进度: {current_size}/512")
                            return f"sync_timeout_at_{current_size}"
                        
                        time.sleep(1)
                    
                    sync_time = time.time() - queue_start
                    print(f"✅ 进程同步完成: {sync_time:.2f}s")
                    
                    # 发送同步信号
                    with cond:
                        cond.notify_all()
                    
                    # 等待任务完成
                    completed = 0
                    errors = []
                    
                    for i, future in enumerate(futures):
                        try:
                            result = future.result(timeout=1)
                            if "success" in str(result):
                                completed += 1
                            else:
                                errors.append(str(result))
                        except Exception as e:
                            errors.append(f"future_error_{e}")
                        
                        if i >= 19:  # 检查前20个任务
                            break
                    
                    total_time = time.time() - start_time
                    
                    print(f"✅ VectorDBBench模拟测试完成:")
                    print(f"   总时间: {total_time:.2f}s")
                    print(f"   成功任务: {completed}/20")
                    print(f"   错误数量: {len(errors)}")
                    
                    if errors:
                        print(f"   错误样本: {errors[:3]}")
                    
                    return "vectordb_simulation_success"
                    
        except Exception as e:
            print(f"❌ VectorDBBench模拟失败: {e}")
            return f"vectordb_simulation_failed: {e}"
    
    def run_targeted_diagnosis(self):
        """运行针对性诊断"""
        print("🎯 VectorDBBench 512并发卡住问题针对性诊断")
        print("=" * 60)
        
        # 1. 检查前置条件
        self.check_prerequisites()
        
        # 2. 分阶段进程创建测试
        process_limit = self.test_process_creation_stages()
        
        if process_limit:
            print(f"\n❌ 发现进程创建瓶颈: 在{process_limit}个进程时失败")
            print(f"💡 建议: 将并发数限制在{process_limit - 50}以下")
            return
        
        # 3. 测试512进程 + Manager
        manager_result = self.test_multiprocessing_manager_with_512()
        
        if "stalled" in manager_result or "timeout" in manager_result:
            print(f"\n❌ 发现Manager瓶颈: {manager_result}")
            print(f"💡 这很可能是VectorDBBench 512并发卡住的根本原因")
            return
        
        # 4. VectorDBBench特定测试
        vectordb_result = self.test_vectordbbench_specific_bottleneck()
        
        print(f"\n📋 诊断结果总结:")
        print(f"   进程创建: {'✅ 正常' if not process_limit else f'❌ 限制在{process_limit}'}")
        print(f"   Manager性能: {'✅ 正常' if manager_result == 'success' else f'❌ {manager_result}'}")
        print(f"   VectorDBBench模拟: {'✅ 正常' if 'success' in vectordb_result else f'❌ {vectordb_result}'}")
        
        if all([
            not process_limit,
            manager_result == "success",
            "success" in vectordb_result
        ]):
            print(f"\n🎉 诊断结果: 512并发应该能正常工作!")
            print(f"💡 建议: 重新尝试VectorDBBench 512并发测试")
        else:
            print(f"\n🔧 需要解决的问题:")
            if process_limit:
                print(f"   1. 进程数限制: 减少到{process_limit - 50}以下")
            if manager_result != "success":
                print(f"   2. Manager性能问题: {manager_result}")
            if "success" not in vectordb_result:
                print(f"   3. VectorDBBench特定问题: {vectordb_result}")

def main():
    # 确保环境变量设置
    os.environ['DATASET_LOCAL_DIR'] = '/nas/yvan.chen/milvus/dataset'
    
    diagnostic = VectorDBBench512Diagnostic()
    diagnostic.run_targeted_diagnosis()

if __name__ == "__main__":
    main()
