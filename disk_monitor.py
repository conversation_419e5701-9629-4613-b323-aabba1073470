#!/usr/bin/env python3
"""
磁盘写入监控脚本 - 自动监控VectorDBBench的磁盘I/O行为
"""

import subprocess
import time
import os
import psutil
import json
import threading
from datetime import datetime
from collections import defaultdict

class DiskWriteMonitor:
    def __init__(self):
        self.monitoring = False
        self.io_data = []
        self.process_data = {}
        self.start_time = None
        
    def get_vectordb_processes(self):
        """获取所有VectorDBBench相关进程"""
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
            try:
                cmdline = ' '.join(proc.info['cmdline'] or [])
                if ('vectordb_bench' in cmdline or 
                    'python' in proc.info['name'].lower() and 'vectordb' in cmdline):
                    processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'cmdline': cmdline,
                        'create_time': proc.info['create_time']
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return processes
    
    def get_process_io_stats(self, pid):
        """获取进程I/O统计"""
        try:
            process = psutil.Process(pid)
            io_counters = process.io_counters()
            memory_info = process.memory_info()
            return {
                'read_bytes': io_counters.read_bytes,
                'write_bytes': io_counters.write_bytes,
                'read_count': io_counters.read_count,
                'write_count': io_counters.write_count,
                'rss': memory_info.rss,
                'vms': memory_info.vms
            }
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            return None
    
    def monitor_system_io(self):
        """监控系统整体I/O"""
        try:
            # 获取磁盘I/O统计
            disk_io = psutil.disk_io_counters()
            if disk_io:
                return {
                    'read_bytes': disk_io.read_bytes,
                    'write_bytes': disk_io.write_bytes,
                    'read_count': disk_io.read_count,
                    'write_count': disk_io.write_count
                }
        except:
            pass
        return None
    
    def monitor_loop(self):
        """监控主循环"""
        print("🔍 开始监控磁盘I/O...")
        self.start_time = time.time()
        
        while self.monitoring:
            timestamp = datetime.now()
            
            # 获取系统I/O统计
            system_io = self.monitor_system_io()
            
            # 获取VectorDBBench进程
            processes = self.get_vectordb_processes()
            
            # 收集进程I/O数据
            process_ios = {}
            for proc in processes:
                pid = proc['pid']
                io_stats = self.get_process_io_stats(pid)
                if io_stats:
                    process_ios[pid] = {
                        'process_info': proc,
                        'io_stats': io_stats
                    }
            
            # 记录数据
            sample = {
                'timestamp': timestamp.isoformat(),
                'elapsed': time.time() - self.start_time,
                'system_io': system_io,
                'processes': process_ios,
                'process_count': len(processes)
            }
            
            self.io_data.append(sample)
            
            # 实时显示
            if len(self.io_data) > 1:
                self.display_realtime_stats(sample)
            
            time.sleep(1)
    
    def display_realtime_stats(self, current_sample):
        """实时显示统计信息"""
        if len(self.io_data) < 2:
            return
            
        prev_sample = self.io_data[-2]
        
        # 计算系统I/O变化
        if (current_sample['system_io'] and prev_sample['system_io']):
            write_diff = (current_sample['system_io']['write_bytes'] - 
                         prev_sample['system_io']['write_bytes'])
            if write_diff > 1024:  # 超过1KB才显示
                print(f"⚠️  系统写入: {write_diff/1024:.1f} KB/s, "
                      f"进程数: {current_sample['process_count']}")
        
        # 检查进程写入
        for pid, proc_data in current_sample['processes'].items():
            if pid in prev_sample['processes']:
                curr_write = proc_data['io_stats']['write_bytes']
                prev_write = prev_sample['processes'][pid]['io_stats']['write_bytes']
                write_diff = curr_write - prev_write
                
                if write_diff > 1024:  # 超过1KB才显示
                    cmdline = proc_data['process_info']['cmdline']
                    print(f"🔴 PID {pid}: {write_diff/1024:.1f} KB/s - {cmdline[:80]}...")
    
    def start_monitoring(self):
        """启动监控"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self.monitor_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if hasattr(self, 'monitor_thread'):
            self.monitor_thread.join(timeout=2)
    
    def analyze_results(self):
        """分析监控结果"""
        if len(self.io_data) < 2:
            print("❌ 监控数据不足")
            return
        
        print("\n" + "="*80)
        print("📊 磁盘写入分析结果")
        print("="*80)
        
        # 分析总体写入量
        total_writes = defaultdict(int)
        max_processes = 0
        
        for i in range(1, len(self.io_data)):
            current = self.io_data[i]
            previous = self.io_data[i-1]
            
            max_processes = max(max_processes, current['process_count'])
            
            # 计算每个进程的写入量
            for pid, proc_data in current['processes'].items():
                if pid in previous['processes']:
                    curr_write = proc_data['io_stats']['write_bytes']
                    prev_write = previous['processes'][pid]['io_stats']['write_bytes']
                    write_diff = curr_write - prev_write
                    
                    if write_diff > 0:
                        total_writes[pid] += write_diff
        
        print(f"📈 监控时长: {self.io_data[-1]['elapsed']:.1f} 秒")
        print(f"📊 最大进程数: {max_processes}")
        
        if total_writes:
            print(f"\n🔴 发现 {len(total_writes)} 个进程有磁盘写入:")
            
            # 按写入量排序
            sorted_writes = sorted(total_writes.items(), 
                                 key=lambda x: x[1], reverse=True)
            
            for i, (pid, bytes_written) in enumerate(sorted_writes[:10]):
                mb_written = bytes_written / (1024 * 1024)
                print(f"\n{i+1}. PID {pid}: {mb_written:.2f} MB")
                
                # 查找进程信息
                proc_info = None
                for sample in reversed(self.io_data):
                    if pid in sample['processes']:
                        proc_info = sample['processes'][pid]['process_info']
                        break
                
                if proc_info:
                    print(f"   命令: {proc_info['cmdline']}")
                    print(f"   进程名: {proc_info['name']}")
                else:
                    print(f"   进程已结束")
        else:
            print("✅ 未发现显著的磁盘写入")
        
        # 保存详细数据
        with open('disk_io_analysis.json', 'w') as f:
            json.dump(self.io_data, f, indent=2, default=str)
        print(f"\n💾 详细数据已保存到: disk_io_analysis.json")

if __name__ == "__main__":
    monitor = DiskWriteMonitor()
    
    try:
        print("🚀 启动磁盘I/O监控...")
        monitor.start_monitoring()
        
        # 等待一段时间让监控稳定
        time.sleep(2)
        
        print("✅ 监控已启动，现在可以运行VectorDBBench测试")
        print("💡 监控将持续运行，按Ctrl+C停止")
        
        # 保持监控运行
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n🛑 停止监控...")
        monitor.stop_monitoring()
        monitor.analyze_results()
