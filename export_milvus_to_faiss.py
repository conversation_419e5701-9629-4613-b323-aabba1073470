#!/usr/bin/env python3
"""
通过Milvus API导出向量数据并创建FAISS索引
复用现有的Milvus数据，避免重新加载
"""

import os
import sys
import time
import numpy as np
import faiss
from pathlib import Path
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/home/<USER>/VectorDBBench/milvus_export.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 输出路径
FAISS_OUTPUT_PATH = "/home/<USER>/VectorDBBench/prebuilt_indexes"

def connect_to_milvus():
    """连接到Milvus"""
    try:
        from pymilvus import connections, utility
        
        logger.info("🔌 连接到Milvus...")
        connections.connect(
            alias="default",
            host="localhost",
            port="19530"
        )
        
        logger.info("✅ Milvus连接成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ Milvus连接失败: {e}")
        return False

def list_collections():
    """列出所有集合"""
    try:
        from pymilvus import utility
        
        collections = utility.list_collections()
        logger.info(f"📋 发现 {len(collections)} 个集合:")
        
        for i, collection_name in enumerate(collections):
            logger.info(f"   {i+1}. {collection_name}")
            
        return collections
        
    except Exception as e:
        logger.error(f"❌ 获取集合列表失败: {e}")
        return []

def analyze_collection(collection_name):
    """分析集合结构"""
    try:
        from pymilvus import Collection
        
        logger.info(f"🔍 分析集合: {collection_name}")
        
        collection = Collection(collection_name)
        
        # 获取集合信息
        logger.info(f"   描述: {collection.description}")
        logger.info(f"   向量数量: {collection.num_entities}")
        
        # 获取schema
        schema = collection.schema
        logger.info(f"   字段数量: {len(schema.fields)}")
        
        vector_field = None
        for field in schema.fields:
            logger.info(f"   字段: {field.name} ({field.dtype})")
            if field.dtype.name in ['FLOAT_VECTOR', 'BINARY_VECTOR']:
                vector_field = field
                logger.info(f"     -> 向量字段: 维度={field.params.get('dim', 'unknown')}")
        
        return collection, vector_field
        
    except Exception as e:
        logger.error(f"❌ 分析集合失败: {e}")
        return None, None

def export_vectors_from_collection(collection, vector_field, limit=10000000):
    """从集合中导出向量"""
    try:
        logger.info(f"📤 导出向量数据...")
        logger.info(f"   集合: {collection.name}")
        logger.info(f"   向量字段: {vector_field.name}")
        logger.info(f"   限制数量: {limit:,}")
        
        # 确保集合已加载
        collection.load()
        logger.info("✅ 集合已加载到内存")
        
        # 查询向量数据
        logger.info("🔍 查询向量数据...")
        
        # 分批查询，避免内存问题
        batch_size = 10000
        all_vectors = []
        total_exported = 0
        
        # Milvus查询限制：offset+limit不能超过16384
        max_window = 16384
        query_batch_size = min(batch_size, max_window)

        logger.info(f"📊 Milvus查询窗口限制: {max_window}, 批次大小: {query_batch_size}")

        # 分批获取向量，确保不超过窗口限制
        offset = 0
        batch_num = 0

        while offset < limit and total_exported < limit:
            # 计算当前批次的限制，确保offset+limit不超过max_window
            remaining_window = max_window - offset
            current_limit = min(query_batch_size, remaining_window, limit - total_exported)

            if current_limit <= 0:
                # 如果窗口用完了，重置offset（这意味着我们可能会重复一些数据）
                logger.info(f"   窗口限制达到，重置offset")
                offset = 0
                remaining_window = max_window
                current_limit = min(query_batch_size, remaining_window, limit - total_exported)

            batch_num += 1
            logger.info(f"   批次 {batch_num}: offset={offset}, limit={current_limit}, 窗口剩余={remaining_window}")

            # 直接查询向量数据
            try:
                batch_results = collection.query(
                    expr="",  # 空表达式表示查询所有
                    output_fields=[vector_field.name],
                    offset=offset,
                    limit=current_limit
                )

                if not batch_results:
                    logger.info(f"   批次 {batch_num} 无数据，可能已到达数据末尾")
                    break

            except Exception as e:
                logger.error(f"   批次 {batch_num} 查询失败: {e}")
                break
            
            # 处理查询结果
            batch_vectors = [result[vector_field.name] for result in batch_results]
            batch_array = np.array(batch_vectors, dtype=np.float32)
            all_vectors.append(batch_array)
            total_exported += len(batch_array)

            logger.info(f"     ✅ 导出 {len(batch_array)} 个向量，累计: {total_exported:,}")

            # 更新offset
            offset += current_limit

            # 避免过度占用内存
            if total_exported >= limit:
                break
        
        if not all_vectors:
            logger.error("❌ 未导出任何向量")
            return None
        
        # 合并所有向量
        logger.info("🔗 合并向量数据...")
        final_vectors = np.vstack(all_vectors)
        
        logger.info(f"✅ 向量导出完成:")
        logger.info(f"   数量: {len(final_vectors):,}")
        logger.info(f"   维度: {final_vectors.shape[1]}")
        logger.info(f"   数据类型: {final_vectors.dtype}")
        logger.info(f"   内存占用: {final_vectors.nbytes / (1024**3):.2f} GB")
        
        return final_vectors
        
    except Exception as e:
        logger.error(f"❌ 导出向量失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def create_faiss_index_from_vectors(vectors):
    """从向量创建FAISS索引"""
    logger.info("🏗️ 创建FAISS索引...")
    
    dim = vectors.shape[1]
    num_vectors = len(vectors)
    
    logger.info(f"📊 索引参数:")
    logger.info(f"   维度: {dim}")
    logger.info(f"   向量数量: {num_vectors:,}")
    
    # 创建HNSW索引
    index = faiss.IndexHNSWFlat(dim, 30)  # M=30
    index.hnsw.efConstruction = 360
    
    logger.info("📝 添加向量到索引...")
    start_time = time.time()
    
    # 分批添加，避免内存问题
    batch_size = 10000
    for i in range(0, num_vectors, batch_size):
        batch = vectors[i:i+batch_size]
        index.add(batch)
        
        if (i + batch_size) % 100000 == 0:
            elapsed = time.time() - start_time
            progress = (i + batch_size) / num_vectors * 100
            logger.info(f"   进度: {progress:.1f}% ({i + batch_size:,}/{num_vectors:,}), 耗时: {elapsed:.1f}s")
    
    total_time = time.time() - start_time
    logger.info(f"✅ 索引创建完成:")
    logger.info(f"   总耗时: {total_time:.1f}s")
    logger.info(f"   索引大小: {index.ntotal:,} 向量")
    
    return index

def save_faiss_index(index, vectors, collection_name):
    """保存FAISS索引"""
    logger.info("💾 保存FAISS索引...")
    
    # 确保输出目录存在
    os.makedirs(FAISS_OUTPUT_PATH, exist_ok=True)
    
    dim = vectors.shape[1]
    num_vectors = len(vectors)
    
    # 生成文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"faiss_hnsw_{dim}d_{num_vectors//1000}k_from_{collection_name}_{timestamp}.index"
    output_path = Path(FAISS_OUTPUT_PATH) / filename
    
    logger.info(f"📁 保存路径: {output_path}")
    
    try:
        faiss.write_index(index, str(output_path))
        file_size = output_path.stat().st_size
        
        logger.info(f"✅ 索引保存成功:")
        logger.info(f"   文件: {filename}")
        logger.info(f"   大小: {file_size / (1024**3):.2f} GB")
        
        return output_path
        
    except Exception as e:
        logger.error(f"❌ 保存索引失败: {e}")
        return None

def main():
    """主函数"""
    logger.info("🎯 Milvus向量导出到FAISS工具")
    logger.info("=" * 60)
    
    # 1. 连接Milvus
    if not connect_to_milvus():
        return False
    
    # 2. 列出集合
    collections = list_collections()
    if not collections:
        logger.error("❌ 未找到任何集合")
        return False
    
    # 3. 选择集合（选择第一个）
    collection_name = collections[0]
    logger.info(f"🎯 选择集合: {collection_name}")
    
    # 4. 分析集合
    collection, vector_field = analyze_collection(collection_name)
    if collection is None or vector_field is None:
        logger.error("❌ 集合分析失败")
        return False
    
    # 5. 导出向量
    vectors = export_vectors_from_collection(collection, vector_field, limit=10000000)
    if vectors is None:
        logger.error("❌ 向量导出失败")
        return False
    
    # 6. 创建FAISS索引
    index = create_faiss_index_from_vectors(vectors)
    if index is None:
        logger.error("❌ 索引创建失败")
        return False
    
    # 7. 保存索引
    output_path = save_faiss_index(index, vectors, collection_name)
    if output_path is None:
        logger.error("❌ 索引保存失败")
        return False
    
    logger.info("🎉 导出完成!")
    logger.info(f"✅ 成功从Milvus集合 '{collection_name}' 创建FAISS索引: {output_path}")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("🛑 用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
