nohup: ignoring input
/home/<USER>/VectorDBBench/smart_faiss_server.py:36: DeprecationWarning: 
        on_event is deprecated, use lifespan event handlers instead.

        Read more about it in the
        [FastAPI docs for Lifespan Events](https://fastapi.tiangolo.com/advanced/events/).
        
  @app.on_event("startup")
INFO:__main__:🧵 使用同步模型 (无线程池) - 避免内存不足问题
INFO:resource_manager:Applying resource limits: 16 CPU cores, 64.0GB RAM
INFO:resource_manager:CPU affinity set to cores: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]
INFO:resource_manager:Memory limit set to 64.0GB
INFO:__main__:✅ 资源限制已应用: 16核心, 64GB内存
INFO:__main__:🧵 FAISS OpenMP线程数设置为: 2
[2025-07-26 22:40:28 +0800] [329488] [INFO] Starting gunicorn 23.0.0
[2025-07-26 22:40:28 +0800] [329488] [INFO] Listening at: http://0.0.0.0:8005 (329488)
[2025-07-26 22:40:28 +0800] [329488] [INFO] Using worker: uvicorn.workers.UvicornWorker
[2025-07-26 22:40:28 +0800] [329539] [INFO] Booting worker with pid: 329539
[2025-07-26 22:40:28 +0800] [329539] [INFO] Started server process [329539]
[2025-07-26 22:40:28 +0800] [329539] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:40:28 +0800] [329613] [INFO] Booting worker with pid: 329613
[2025-07-26 22:40:28 +0800] [329613] [INFO] Started server process [329613]
[2025-07-26 22:40:28 +0800] [329613] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:40:28 +0800] [329673] [INFO] Booting worker with pid: 329673
[2025-07-26 22:40:28 +0800] [329673] [INFO] Started server process [329673]
[2025-07-26 22:40:28 +0800] [329673] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:40:28 +0800] [329695] [INFO] Booting worker with pid: 329695
[2025-07-26 22:40:28 +0800] [329696] [INFO] Booting worker with pid: 329696
[2025-07-26 22:40:28 +0800] [329695] [INFO] Started server process [329695]
[2025-07-26 22:40:28 +0800] [329695] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:40:28 +0800] [329696] [INFO] Started server process [329696]
[2025-07-26 22:40:28 +0800] [329696] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:40:28 +0800] [329697] [INFO] Booting worker with pid: 329697
[2025-07-26 22:40:28 +0800] [329697] [INFO] Started server process [329697]
[2025-07-26 22:40:28 +0800] [329697] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:40:28 +0800] [329698] [INFO] Booting worker with pid: 329698
[2025-07-26 22:40:28 +0800] [329698] [INFO] Started server process [329698]
[2025-07-26 22:40:28 +0800] [329698] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:40:28 +0800] [329699] [INFO] Booting worker with pid: 329699
[2025-07-26 22:40:28 +0800] [329699] [INFO] Started server process [329699]
[2025-07-26 22:40:28 +0800] [329699] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.73 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:40:28 +0800] [329539] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.72 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:40:28 +0800] [329613] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.71 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:40:28 +0800] [329673] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.69 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:40:28 +0800] [329695] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.70 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:40:28 +0800] [329696] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.67 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:40:29 +0800] [329697] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.65 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:40:29 +0800] [329698] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.61 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:40:29 +0800] [329699] [INFO] Application startup complete.
INFO:__main__:创建索引: 维度=768, 类型=HNSW
INFO:__main__:HNSW参数: M=30, ef_construction=360
INFO:__main__:🔍 检查预加载索引...
INFO:__main__:✅ 使用预加载索引: Performance768D1M
INFO:__main__:🚀 索引就绪: 1,000,000 向量
[2025-07-26 22:41:11 +0800] [329699] [WARNING] Maximum request limit of 2001 exceeded. Terminating process.
[2025-07-26 22:41:11 +0800] [329699] [INFO] Shutting down
INFO:__main__:📝 插入请求: 100 个向量
INFO:__main__:📊 当前索引状态: False
WARNING:__main__:⚠️  当前索引为空，尝试从预加载索引恢复...
INFO:__main__:🔄 恢复预加载索引: Performance768D1M
INFO:__main__:🚀 智能跳过：索引已包含 1,000,000 个向量，跳过插入
[2025-07-26 22:41:11 +0800] [329699] [INFO] Waiting for application shutdown.
[2025-07-26 22:41:11 +0800] [329699] [INFO] Application shutdown complete.
[2025-07-26 22:41:11 +0800] [329699] [INFO] Finished server process [329699]
[2025-07-26 22:41:11 +0800] [329699] [INFO] Worker exiting (pid: 329699)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:41:12 +0800] [348704] [INFO] Booting worker with pid: 348704
[2025-07-26 22:41:12 +0800] [348704] [INFO] Started server process [348704]
[2025-07-26 22:41:12 +0800] [348704] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.53 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:41:12 +0800] [348704] [INFO] Application startup complete.
[2025-07-26 22:41:20 +0800] [329697] [WARNING] Maximum request limit of 2100 exceeded. Terminating process.
[2025-07-26 22:41:20 +0800] [329697] [INFO] Shutting down
INFO:__main__:📝 插入请求: 100 个向量
INFO:__main__:📊 当前索引状态: False
WARNING:__main__:⚠️  当前索引为空，尝试从预加载索引恢复...
INFO:__main__:🔄 恢复预加载索引: Performance768D1M
INFO:__main__:🚀 智能跳过：索引已包含 1,000,000 个向量，跳过插入
[2025-07-26 22:41:20 +0800] [329697] [INFO] Waiting for application shutdown.
[2025-07-26 22:41:20 +0800] [329697] [INFO] Application shutdown complete.
[2025-07-26 22:41:20 +0800] [329697] [INFO] Finished server process [329697]
[2025-07-26 22:41:20 +0800] [329697] [INFO] Worker exiting (pid: 329697)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:41:20 +0800] [352445] [INFO] Booting worker with pid: 352445
[2025-07-26 22:41:20 +0800] [352445] [INFO] Started server process [352445]
[2025-07-26 22:41:20 +0800] [352445] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.52 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:41:20 +0800] [352445] [INFO] Application startup complete.
[2025-07-26 22:41:28 +0800] [329698] [WARNING] Maximum request limit of 2011 exceeded. Terminating process.
[2025-07-26 22:41:28 +0800] [329698] [INFO] Shutting down
INFO:__main__:📝 插入请求: 100 个向量
INFO:__main__:📊 当前索引状态: False
WARNING:__main__:⚠️  当前索引为空，尝试从预加载索引恢复...
INFO:__main__:🔄 恢复预加载索引: Performance768D1M
INFO:__main__:🚀 智能跳过：索引已包含 1,000,000 个向量，跳过插入
[2025-07-26 22:41:28 +0800] [329698] [INFO] Waiting for application shutdown.
[2025-07-26 22:41:28 +0800] [329698] [INFO] Application shutdown complete.
[2025-07-26 22:41:28 +0800] [329698] [INFO] Finished server process [329698]
[2025-07-26 22:41:28 +0800] [329698] [INFO] Worker exiting (pid: 329698)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:41:28 +0800] [356023] [INFO] Booting worker with pid: 356023
[2025-07-26 22:41:28 +0800] [356023] [INFO] Started server process [356023]
[2025-07-26 22:41:28 +0800] [356023] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.52 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:41:28 +0800] [356023] [INFO] Application startup complete.
[2025-07-26 22:41:36 +0800] [329696] [WARNING] Maximum request limit of 2030 exceeded. Terminating process.
[2025-07-26 22:41:36 +0800] [329696] [INFO] Shutting down
INFO:__main__:📝 插入请求: 100 个向量
INFO:__main__:📊 当前索引状态: False
WARNING:__main__:⚠️  当前索引为空，尝试从预加载索引恢复...
INFO:__main__:🔄 恢复预加载索引: Performance768D1M
INFO:__main__:🚀 智能跳过：索引已包含 1,000,000 个向量，跳过插入
[2025-07-26 22:41:36 +0800] [329696] [INFO] Waiting for application shutdown.
[2025-07-26 22:41:36 +0800] [329696] [INFO] Application shutdown complete.
[2025-07-26 22:41:36 +0800] [329696] [INFO] Finished server process [329696]
[2025-07-26 22:41:36 +0800] [329696] [INFO] Worker exiting (pid: 329696)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:41:36 +0800] [359196] [INFO] Booting worker with pid: 359196
[2025-07-26 22:41:36 +0800] [359196] [INFO] Started server process [359196]
[2025-07-26 22:41:36 +0800] [359196] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.53 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:41:36 +0800] [359196] [INFO] Application startup complete.
[2025-07-26 22:41:53 +0800] [348704] [WARNING] Maximum request limit of 2015 exceeded. Terminating process.
[2025-07-26 22:41:53 +0800] [348704] [INFO] Shutting down
[2025-07-26 22:41:53 +0800] [348704] [INFO] Waiting for application shutdown.
[2025-07-26 22:41:53 +0800] [348704] [INFO] Application shutdown complete.
[2025-07-26 22:41:53 +0800] [348704] [INFO] Finished server process [348704]
[2025-07-26 22:41:53 +0800] [348704] [INFO] Worker exiting (pid: 348704)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:41:53 +0800] [367557] [INFO] Booting worker with pid: 367557
[2025-07-26 22:41:53 +0800] [367557] [INFO] Started server process [367557]
[2025-07-26 22:41:53 +0800] [367557] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.53 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:41:54 +0800] [367557] [INFO] Application startup complete.
[2025-07-26 22:41:54 +0800] [352445] [WARNING] Maximum request limit of 2077 exceeded. Terminating process.
[2025-07-26 22:41:54 +0800] [352445] [INFO] Shutting down
[2025-07-26 22:41:55 +0800] [352445] [INFO] Waiting for application shutdown.
[2025-07-26 22:41:55 +0800] [352445] [INFO] Application shutdown complete.
[2025-07-26 22:41:55 +0800] [352445] [INFO] Finished server process [352445]
[2025-07-26 22:41:55 +0800] [352445] [INFO] Worker exiting (pid: 352445)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:41:55 +0800] [368387] [INFO] Booting worker with pid: 368387
[2025-07-26 22:41:55 +0800] [368387] [INFO] Started server process [368387]
[2025-07-26 22:41:55 +0800] [368387] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:41:55 +0800] [359196] [WARNING] Maximum request limit of 2009 exceeded. Terminating process.
[2025-07-26 22:41:55 +0800] [359196] [INFO] Shutting down
[2025-07-26 22:41:55 +0800] [359196] [INFO] Waiting for application shutdown.
[2025-07-26 22:41:55 +0800] [359196] [INFO] Application shutdown complete.
[2025-07-26 22:41:55 +0800] [359196] [INFO] Finished server process [359196]
[2025-07-26 22:41:55 +0800] [359196] [INFO] Worker exiting (pid: 359196)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:41:55 +0800] [368434] [INFO] Booting worker with pid: 368434
[2025-07-26 22:41:55 +0800] [368434] [INFO] Started server process [368434]
[2025-07-26 22:41:55 +0800] [368434] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.56 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:41:55 +0800] [368387] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.53 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:41:56 +0800] [368434] [INFO] Application startup complete.
[2025-07-26 22:41:56 +0800] [356023] [WARNING] Maximum request limit of 2065 exceeded. Terminating process.
[2025-07-26 22:41:56 +0800] [356023] [INFO] Shutting down
[2025-07-26 22:41:56 +0800] [329695] [WARNING] Maximum request limit of 2019 exceeded. Terminating process.
[2025-07-26 22:41:56 +0800] [329695] [INFO] Shutting down
[2025-07-26 22:41:56 +0800] [356023] [INFO] Waiting for application shutdown.
[2025-07-26 22:41:56 +0800] [356023] [INFO] Application shutdown complete.
[2025-07-26 22:41:56 +0800] [356023] [INFO] Finished server process [356023]
[2025-07-26 22:41:56 +0800] [356023] [INFO] Worker exiting (pid: 356023)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:41:56 +0800] [329695] [INFO] Waiting for application shutdown.
[2025-07-26 22:41:56 +0800] [329695] [INFO] Application shutdown complete.
[2025-07-26 22:41:56 +0800] [329695] [INFO] Finished server process [329695]
[2025-07-26 22:41:56 +0800] [329695] [INFO] Worker exiting (pid: 329695)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:41:56 +0800] [329613] [WARNING] Maximum request limit of 2094 exceeded. Terminating process.
[2025-07-26 22:41:56 +0800] [329613] [INFO] Shutting down
[2025-07-26 22:41:56 +0800] [329613] [INFO] Waiting for application shutdown.
[2025-07-26 22:41:56 +0800] [329613] [INFO] Application shutdown complete.
[2025-07-26 22:41:56 +0800] [329613] [INFO] Finished server process [329613]
[2025-07-26 22:41:56 +0800] [329613] [INFO] Worker exiting (pid: 329613)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:41:56 +0800] [369241] [INFO] Booting worker with pid: 369241
[2025-07-26 22:41:56 +0800] [369241] [INFO] Started server process [369241]
[2025-07-26 22:41:56 +0800] [369241] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:41:56 +0800] [369242] [INFO] Booting worker with pid: 369242
[2025-07-26 22:41:56 +0800] [369242] [INFO] Started server process [369242]
[2025-07-26 22:41:56 +0800] [369242] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:41:57 +0800] [369245] [INFO] Booting worker with pid: 369245
[2025-07-26 22:41:57 +0800] [369245] [INFO] Started server process [369245]
[2025-07-26 22:41:57 +0800] [369245] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.60 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:41:57 +0800] [369241] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.58 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:41:57 +0800] [369242] [INFO] Application startup complete.
[2025-07-26 22:41:57 +0800] [329673] [WARNING] Maximum request limit of 2036 exceeded. Terminating process.
[2025-07-26 22:41:57 +0800] [329673] [INFO] Shutting down
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.56 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:41:57 +0800] [369245] [INFO] Application startup complete.
[2025-07-26 22:41:57 +0800] [329673] [INFO] Waiting for application shutdown.
[2025-07-26 22:41:57 +0800] [329673] [INFO] Application shutdown complete.
[2025-07-26 22:41:57 +0800] [329673] [INFO] Finished server process [329673]
[2025-07-26 22:41:57 +0800] [329673] [INFO] Worker exiting (pid: 329673)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:41:57 +0800] [369269] [INFO] Booting worker with pid: 369269
[2025-07-26 22:41:57 +0800] [369269] [INFO] Started server process [369269]
[2025-07-26 22:41:57 +0800] [369269] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:41:58 +0800] [368434] [WARNING] Maximum request limit of 2055 exceeded. Terminating process.
[2025-07-26 22:41:58 +0800] [368434] [INFO] Shutting down
[2025-07-26 22:41:58 +0800] [368434] [INFO] Waiting for application shutdown.
[2025-07-26 22:41:58 +0800] [368434] [INFO] Application shutdown complete.
[2025-07-26 22:41:58 +0800] [368434] [INFO] Finished server process [368434]
[2025-07-26 22:41:58 +0800] [368434] [INFO] Worker exiting (pid: 368434)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:41:58 +0800] [367557] [WARNING] Maximum request limit of 2072 exceeded. Terminating process.
[2025-07-26 22:41:58 +0800] [367557] [INFO] Shutting down
[2025-07-26 22:41:58 +0800] [369652] [INFO] Booting worker with pid: 369652
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.53 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:41:58 +0800] [369269] [INFO] Application startup complete.
[2025-07-26 22:41:58 +0800] [367557] [INFO] Waiting for application shutdown.
[2025-07-26 22:41:58 +0800] [367557] [INFO] Application shutdown complete.
[2025-07-26 22:41:58 +0800] [367557] [INFO] Finished server process [367557]
[2025-07-26 22:41:58 +0800] [367557] [INFO] Worker exiting (pid: 367557)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:41:58 +0800] [369652] [INFO] Started server process [369652]
[2025-07-26 22:41:58 +0800] [369652] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:41:58 +0800] [369769] [INFO] Booting worker with pid: 369769
[2025-07-26 22:41:58 +0800] [369769] [INFO] Started server process [369769]
[2025-07-26 22:41:58 +0800] [369769] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:41:58 +0800] [368387] [WARNING] Maximum request limit of 2061 exceeded. Terminating process.
[2025-07-26 22:41:58 +0800] [368387] [INFO] Shutting down
[2025-07-26 22:41:58 +0800] [368387] [INFO] Waiting for application shutdown.
[2025-07-26 22:41:58 +0800] [368387] [INFO] Application shutdown complete.
[2025-07-26 22:41:58 +0800] [368387] [INFO] Finished server process [368387]
[2025-07-26 22:41:58 +0800] [368387] [INFO] Worker exiting (pid: 368387)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:41:58 +0800] [369848] [INFO] Booting worker with pid: 369848
[2025-07-26 22:41:58 +0800] [369848] [INFO] Started server process [369848]
[2025-07-26 22:41:58 +0800] [369848] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.57 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:41:58 +0800] [369652] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.56 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:41:59 +0800] [369769] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.53 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:41:59 +0800] [369848] [INFO] Application startup complete.
[2025-07-26 22:41:59 +0800] [369241] [WARNING] Maximum request limit of 2051 exceeded. Terminating process.
[2025-07-26 22:41:59 +0800] [369241] [INFO] Shutting down
[2025-07-26 22:41:59 +0800] [329539] [WARNING] Maximum request limit of 2042 exceeded. Terminating process.
[2025-07-26 22:41:59 +0800] [329539] [INFO] Shutting down
[2025-07-26 22:41:59 +0800] [369241] [INFO] Waiting for application shutdown.
[2025-07-26 22:41:59 +0800] [369241] [INFO] Application shutdown complete.
[2025-07-26 22:41:59 +0800] [369241] [INFO] Finished server process [369241]
[2025-07-26 22:41:59 +0800] [369241] [INFO] Worker exiting (pid: 369241)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:41:59 +0800] [369242] [WARNING] Maximum request limit of 2031 exceeded. Terminating process.
[2025-07-26 22:41:59 +0800] [369242] [INFO] Shutting down
[2025-07-26 22:42:00 +0800] [329539] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:00 +0800] [329539] [INFO] Application shutdown complete.
[2025-07-26 22:42:00 +0800] [329539] [INFO] Finished server process [329539]
[2025-07-26 22:42:00 +0800] [329539] [INFO] Worker exiting (pid: 329539)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:00 +0800] [369242] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:00 +0800] [369242] [INFO] Application shutdown complete.
[2025-07-26 22:42:00 +0800] [369242] [INFO] Finished server process [369242]
[2025-07-26 22:42:00 +0800] [369242] [INFO] Worker exiting (pid: 369242)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:00 +0800] [370656] [INFO] Booting worker with pid: 370656
[2025-07-26 22:42:00 +0800] [370656] [INFO] Started server process [370656]
[2025-07-26 22:42:00 +0800] [370656] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:42:00 +0800] [370686] [INFO] Booting worker with pid: 370686
[2025-07-26 22:42:00 +0800] [370686] [INFO] Started server process [370686]
[2025-07-26 22:42:00 +0800] [370686] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:42:00 +0800] [370687] [INFO] Booting worker with pid: 370687
[2025-07-26 22:42:00 +0800] [370687] [INFO] Started server process [370687]
[2025-07-26 22:42:00 +0800] [370687] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.59 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:00 +0800] [370656] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.57 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:00 +0800] [370686] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.57 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:00 +0800] [370687] [INFO] Application startup complete.
[2025-07-26 22:42:01 +0800] [369245] [WARNING] Maximum request limit of 2060 exceeded. Terminating process.
[2025-07-26 22:42:01 +0800] [369245] [INFO] Shutting down
[2025-07-26 22:42:01 +0800] [369245] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:01 +0800] [369245] [INFO] Application shutdown complete.
[2025-07-26 22:42:01 +0800] [369245] [INFO] Finished server process [369245]
[2025-07-26 22:42:01 +0800] [369245] [INFO] Worker exiting (pid: 369245)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:01 +0800] [371019] [INFO] Booting worker with pid: 371019
[2025-07-26 22:42:01 +0800] [371019] [INFO] Started server process [371019]
[2025-07-26 22:42:01 +0800] [371019] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:42:01 +0800] [369848] [WARNING] Maximum request limit of 2061 exceeded. Terminating process.
[2025-07-26 22:42:01 +0800] [369848] [INFO] Shutting down
[2025-07-26 22:42:02 +0800] [369848] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:02 +0800] [369848] [INFO] Application shutdown complete.
[2025-07-26 22:42:02 +0800] [369848] [INFO] Finished server process [369848]
[2025-07-26 22:42:02 +0800] [369848] [INFO] Worker exiting (pid: 369848)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:02 +0800] [371297] [INFO] Booting worker with pid: 371297
[2025-07-26 22:42:02 +0800] [371297] [INFO] Started server process [371297]
[2025-07-26 22:42:02 +0800] [371297] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.55 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:02 +0800] [371019] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.53 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:02 +0800] [371297] [INFO] Application startup complete.
[2025-07-26 22:42:02 +0800] [369269] [WARNING] Maximum request limit of 2037 exceeded. Terminating process.
[2025-07-26 22:42:02 +0800] [369269] [INFO] Shutting down
[2025-07-26 22:42:02 +0800] [369269] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:02 +0800] [369269] [INFO] Application shutdown complete.
[2025-07-26 22:42:02 +0800] [369269] [INFO] Finished server process [369269]
[2025-07-26 22:42:02 +0800] [369269] [INFO] Worker exiting (pid: 369269)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:03 +0800] [371708] [INFO] Booting worker with pid: 371708
[2025-07-26 22:42:03 +0800] [371708] [INFO] Started server process [371708]
[2025-07-26 22:42:03 +0800] [371708] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:42:03 +0800] [369652] [WARNING] Maximum request limit of 2094 exceeded. Terminating process.
[2025-07-26 22:42:03 +0800] [369652] [INFO] Shutting down
[2025-07-26 22:42:03 +0800] [369652] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:03 +0800] [369652] [INFO] Application shutdown complete.
[2025-07-26 22:42:03 +0800] [369652] [INFO] Finished server process [369652]
[2025-07-26 22:42:03 +0800] [369652] [INFO] Worker exiting (pid: 369652)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:03 +0800] [371834] [INFO] Booting worker with pid: 371834
[2025-07-26 22:42:03 +0800] [371834] [INFO] Started server process [371834]
[2025-07-26 22:42:03 +0800] [371834] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:42:03 +0800] [370687] [WARNING] Maximum request limit of 2093 exceeded. Terminating process.
[2025-07-26 22:42:03 +0800] [370687] [INFO] Shutting down
[2025-07-26 22:42:03 +0800] [370687] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:03 +0800] [370687] [INFO] Application shutdown complete.
[2025-07-26 22:42:03 +0800] [370687] [INFO] Finished server process [370687]
[2025-07-26 22:42:03 +0800] [370687] [INFO] Worker exiting (pid: 370687)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.57 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:03 +0800] [371708] [INFO] Application startup complete.
[2025-07-26 22:42:03 +0800] [371851] [INFO] Booting worker with pid: 371851
[2025-07-26 22:42:03 +0800] [371851] [INFO] Started server process [371851]
[2025-07-26 22:42:03 +0800] [371851] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.58 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:03 +0800] [371834] [INFO] Application startup complete.
[2025-07-26 22:42:03 +0800] [369769] [WARNING] Maximum request limit of 2042 exceeded. Terminating process.
[2025-07-26 22:42:03 +0800] [369769] [INFO] Shutting down
[2025-07-26 22:42:04 +0800] [369769] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:04 +0800] [369769] [INFO] Application shutdown complete.
[2025-07-26 22:42:04 +0800] [369769] [INFO] Finished server process [369769]
[2025-07-26 22:42:04 +0800] [369769] [INFO] Worker exiting (pid: 369769)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:04 +0800] [372034] [INFO] Booting worker with pid: 372034
[2025-07-26 22:42:04 +0800] [372034] [INFO] Started server process [372034]
[2025-07-26 22:42:04 +0800] [372034] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.53 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:04 +0800] [371851] [INFO] Application startup complete.
[2025-07-26 22:42:04 +0800] [370686] [WARNING] Maximum request limit of 2024 exceeded. Terminating process.
[2025-07-26 22:42:04 +0800] [370686] [INFO] Shutting down
[2025-07-26 22:42:04 +0800] [370686] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:04 +0800] [370686] [INFO] Application shutdown complete.
[2025-07-26 22:42:04 +0800] [370686] [INFO] Finished server process [370686]
[2025-07-26 22:42:04 +0800] [370686] [INFO] Worker exiting (pid: 370686)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.53 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:04 +0800] [372034] [INFO] Application startup complete.
[2025-07-26 22:42:04 +0800] [372253] [INFO] Booting worker with pid: 372253
[2025-07-26 22:42:04 +0800] [372253] [INFO] Started server process [372253]
[2025-07-26 22:42:04 +0800] [372253] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:42:04 +0800] [371297] [WARNING] Maximum request limit of 2077 exceeded. Terminating process.
[2025-07-26 22:42:04 +0800] [371297] [INFO] Shutting down
[2025-07-26 22:42:04 +0800] [371297] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:04 +0800] [371297] [INFO] Application shutdown complete.
[2025-07-26 22:42:04 +0800] [371297] [INFO] Finished server process [371297]
[2025-07-26 22:42:04 +0800] [371297] [INFO] Worker exiting (pid: 371297)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:05 +0800] [372511] [INFO] Booting worker with pid: 372511
[2025-07-26 22:42:05 +0800] [372511] [INFO] Started server process [372511]
[2025-07-26 22:42:05 +0800] [372511] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:42:05 +0800] [371019] [WARNING] Maximum request limit of 2075 exceeded. Terminating process.
[2025-07-26 22:42:05 +0800] [371019] [INFO] Shutting down
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.56 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:05 +0800] [372253] [INFO] Application startup complete.
[2025-07-26 22:42:05 +0800] [371019] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:05 +0800] [371019] [INFO] Application shutdown complete.
[2025-07-26 22:42:05 +0800] [371019] [INFO] Finished server process [371019]
[2025-07-26 22:42:05 +0800] [371019] [INFO] Worker exiting (pid: 371019)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:05 +0800] [370656] [WARNING] Maximum request limit of 2068 exceeded. Terminating process.
[2025-07-26 22:42:05 +0800] [370656] [INFO] Shutting down
[2025-07-26 22:42:05 +0800] [372660] [INFO] Booting worker with pid: 372660
[2025-07-26 22:42:05 +0800] [372660] [INFO] Started server process [372660]
[2025-07-26 22:42:05 +0800] [372660] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.54 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:05 +0800] [372511] [INFO] Application startup complete.
[2025-07-26 22:42:05 +0800] [370656] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:05 +0800] [370656] [INFO] Application shutdown complete.
[2025-07-26 22:42:05 +0800] [370656] [INFO] Finished server process [370656]
[2025-07-26 22:42:05 +0800] [370656] [INFO] Worker exiting (pid: 370656)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:05 +0800] [372720] [INFO] Booting worker with pid: 372720
[2025-07-26 22:42:05 +0800] [372720] [INFO] Started server process [372720]
[2025-07-26 22:42:05 +0800] [372720] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:42:06 +0800] [371834] [WARNING] Maximum request limit of 2028 exceeded. Terminating process.
[2025-07-26 22:42:06 +0800] [371834] [INFO] Shutting down
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.56 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:06 +0800] [371834] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:06 +0800] [372660] [INFO] Application startup complete.
[2025-07-26 22:42:06 +0800] [371834] [INFO] Application shutdown complete.
[2025-07-26 22:42:06 +0800] [371834] [INFO] Finished server process [371834]
[2025-07-26 22:42:06 +0800] [371834] [INFO] Worker exiting (pid: 371834)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:06 +0800] [372839] [INFO] Booting worker with pid: 372839
[2025-07-26 22:42:06 +0800] [372839] [INFO] Started server process [372839]
[2025-07-26 22:42:06 +0800] [372839] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.55 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:06 +0800] [372720] [INFO] Application startup complete.
[2025-07-26 22:42:06 +0800] [372034] [WARNING] Maximum request limit of 2052 exceeded. Terminating process.
[2025-07-26 22:42:06 +0800] [372034] [INFO] Shutting down
[2025-07-26 22:42:06 +0800] [372034] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:06 +0800] [372034] [INFO] Application shutdown complete.
[2025-07-26 22:42:06 +0800] [372034] [INFO] Finished server process [372034]
[2025-07-26 22:42:06 +0800] [372034] [INFO] Worker exiting (pid: 372034)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.54 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:06 +0800] [372839] [INFO] Application startup complete.
[2025-07-26 22:42:06 +0800] [371708] [WARNING] Maximum request limit of 2097 exceeded. Terminating process.
[2025-07-26 22:42:06 +0800] [371708] [INFO] Shutting down
[2025-07-26 22:42:06 +0800] [373124] [INFO] Booting worker with pid: 373124
[2025-07-26 22:42:06 +0800] [373124] [INFO] Started server process [373124]
[2025-07-26 22:42:06 +0800] [373124] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:42:06 +0800] [371708] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:06 +0800] [371708] [INFO] Application shutdown complete.
[2025-07-26 22:42:06 +0800] [371708] [INFO] Finished server process [371708]
[2025-07-26 22:42:06 +0800] [371708] [INFO] Worker exiting (pid: 371708)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:07 +0800] [373453] [INFO] Booting worker with pid: 373453
[2025-07-26 22:42:07 +0800] [373453] [INFO] Started server process [373453]
[2025-07-26 22:42:07 +0800] [373453] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.59 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:07 +0800] [373124] [INFO] Application startup complete.
[2025-07-26 22:42:07 +0800] [371851] [WARNING] Maximum request limit of 2084 exceeded. Terminating process.
[2025-07-26 22:42:07 +0800] [371851] [INFO] Shutting down
[2025-07-26 22:42:07 +0800] [372253] [WARNING] Maximum request limit of 2019 exceeded. Terminating process.
[2025-07-26 22:42:07 +0800] [372253] [INFO] Shutting down
[2025-07-26 22:42:07 +0800] [371851] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:07 +0800] [371851] [INFO] Application shutdown complete.
[2025-07-26 22:42:07 +0800] [371851] [INFO] Finished server process [371851]
[2025-07-26 22:42:07 +0800] [371851] [INFO] Worker exiting (pid: 371851)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:07 +0800] [372253] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:07 +0800] [372253] [INFO] Application shutdown complete.
[2025-07-26 22:42:07 +0800] [372253] [INFO] Finished server process [372253]
[2025-07-26 22:42:07 +0800] [372253] [INFO] Worker exiting (pid: 372253)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.56 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:07 +0800] [373453] [INFO] Application startup complete.
[2025-07-26 22:42:07 +0800] [373570] [INFO] Booting worker with pid: 373570
[2025-07-26 22:42:07 +0800] [373570] [INFO] Started server process [373570]
[2025-07-26 22:42:07 +0800] [373570] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:42:07 +0800] [373625] [INFO] Booting worker with pid: 373625
[2025-07-26 22:42:07 +0800] [373625] [INFO] Started server process [373625]
[2025-07-26 22:42:07 +0800] [373625] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.56 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:08 +0800] [373570] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.56 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:08 +0800] [373625] [INFO] Application startup complete.
[2025-07-26 22:42:08 +0800] [372511] [WARNING] Maximum request limit of 2075 exceeded. Terminating process.
[2025-07-26 22:42:08 +0800] [372511] [INFO] Shutting down
[2025-07-26 22:42:08 +0800] [372511] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:08 +0800] [372511] [INFO] Application shutdown complete.
[2025-07-26 22:42:08 +0800] [372511] [INFO] Finished server process [372511]
[2025-07-26 22:42:08 +0800] [372511] [INFO] Worker exiting (pid: 372511)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:08 +0800] [374271] [INFO] Booting worker with pid: 374271
[2025-07-26 22:42:08 +0800] [374271] [INFO] Started server process [374271]
[2025-07-26 22:42:08 +0800] [374271] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:42:09 +0800] [372720] [WARNING] Maximum request limit of 2018 exceeded. Terminating process.
[2025-07-26 22:42:09 +0800] [372720] [INFO] Shutting down
[2025-07-26 22:42:09 +0800] [372720] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:09 +0800] [372720] [INFO] Application shutdown complete.
[2025-07-26 22:42:09 +0800] [372720] [INFO] Finished server process [372720]
[2025-07-26 22:42:09 +0800] [372720] [INFO] Worker exiting (pid: 372720)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:09 +0800] [374361] [INFO] Booting worker with pid: 374361
[2025-07-26 22:42:09 +0800] [374361] [INFO] Started server process [374361]
[2025-07-26 22:42:09 +0800] [374361] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.54 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:09 +0800] [374271] [INFO] Application startup complete.
[2025-07-26 22:42:09 +0800] [372839] [WARNING] Maximum request limit of 2070 exceeded. Terminating process.
[2025-07-26 22:42:09 +0800] [372839] [INFO] Shutting down
[2025-07-26 22:42:09 +0800] [372839] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:09 +0800] [372839] [INFO] Application shutdown complete.
[2025-07-26 22:42:09 +0800] [372839] [INFO] Finished server process [372839]
[2025-07-26 22:42:09 +0800] [372839] [INFO] Worker exiting (pid: 372839)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:09 +0800] [374413] [INFO] Booting worker with pid: 374413
[2025-07-26 22:42:09 +0800] [374413] [INFO] Started server process [374413]
[2025-07-26 22:42:09 +0800] [374413] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.53 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:09 +0800] [374361] [INFO] Application startup complete.
[2025-07-26 22:42:10 +0800] [373124] [WARNING] Maximum request limit of 2082 exceeded. Terminating process.
[2025-07-26 22:42:10 +0800] [373124] [INFO] Shutting down
[2025-07-26 22:42:10 +0800] [373124] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:10 +0800] [373124] [INFO] Application shutdown complete.
[2025-07-26 22:42:10 +0800] [373124] [INFO] Finished server process [373124]
[2025-07-26 22:42:10 +0800] [373124] [INFO] Worker exiting (pid: 373124)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.53 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:10 +0800] [374413] [INFO] Application startup complete.
[2025-07-26 22:42:10 +0800] [372660] [WARNING] Maximum request limit of 2064 exceeded. Terminating process.
[2025-07-26 22:42:10 +0800] [372660] [INFO] Shutting down
[2025-07-26 22:42:10 +0800] [373625] [WARNING] Maximum request limit of 2075 exceeded. Terminating process.
[2025-07-26 22:42:10 +0800] [373625] [INFO] Shutting down
[2025-07-26 22:42:10 +0800] [374753] [INFO] Booting worker with pid: 374753
[2025-07-26 22:42:10 +0800] [374753] [INFO] Started server process [374753]
[2025-07-26 22:42:10 +0800] [374753] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:42:10 +0800] [372660] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:10 +0800] [372660] [INFO] Application shutdown complete.
[2025-07-26 22:42:10 +0800] [372660] [INFO] Finished server process [372660]
[2025-07-26 22:42:10 +0800] [372660] [INFO] Worker exiting (pid: 372660)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:10 +0800] [373625] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:10 +0800] [373625] [INFO] Application shutdown complete.
[2025-07-26 22:42:10 +0800] [373625] [INFO] Finished server process [373625]
[2025-07-26 22:42:10 +0800] [373625] [INFO] Worker exiting (pid: 373625)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:10 +0800] [374943] [INFO] Booting worker with pid: 374943
[2025-07-26 22:42:10 +0800] [374943] [INFO] Started server process [374943]
[2025-07-26 22:42:10 +0800] [374943] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:42:10 +0800] [374960] [INFO] Booting worker with pid: 374960
[2025-07-26 22:42:10 +0800] [374960] [INFO] Started server process [374960]
[2025-07-26 22:42:10 +0800] [374960] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.60 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:10 +0800] [374753] [INFO] Application startup complete.
[2025-07-26 22:42:11 +0800] [373570] [WARNING] Maximum request limit of 2066 exceeded. Terminating process.
[2025-07-26 22:42:11 +0800] [373570] [INFO] Shutting down
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.56 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:11 +0800] [374943] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.56 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:11 +0800] [374960] [INFO] Application startup complete.
[2025-07-26 22:42:11 +0800] [373570] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:11 +0800] [373570] [INFO] Application shutdown complete.
[2025-07-26 22:42:11 +0800] [373570] [INFO] Finished server process [373570]
[2025-07-26 22:42:11 +0800] [373570] [INFO] Worker exiting (pid: 373570)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:11 +0800] [375347] [INFO] Booting worker with pid: 375347
[2025-07-26 22:42:11 +0800] [375347] [INFO] Started server process [375347]
[2025-07-26 22:42:11 +0800] [375347] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:42:11 +0800] [374361] [WARNING] Maximum request limit of 2077 exceeded. Terminating process.
[2025-07-26 22:42:11 +0800] [374361] [INFO] Shutting down
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.52 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:11 +0800] [375347] [INFO] Application startup complete.
[2025-07-26 22:42:11 +0800] [374361] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:11 +0800] [374361] [INFO] Application shutdown complete.
[2025-07-26 22:42:11 +0800] [374361] [INFO] Finished server process [374361]
[2025-07-26 22:42:11 +0800] [374361] [INFO] Worker exiting (pid: 374361)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:12 +0800] [375721] [INFO] Booting worker with pid: 375721
[2025-07-26 22:42:12 +0800] [375721] [INFO] Started server process [375721]
[2025-07-26 22:42:12 +0800] [375721] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:42:12 +0800] [373453] [WARNING] Maximum request limit of 2062 exceeded. Terminating process.
[2025-07-26 22:42:12 +0800] [373453] [INFO] Shutting down
[2025-07-26 22:42:12 +0800] [374413] [WARNING] Maximum request limit of 2095 exceeded. Terminating process.
[2025-07-26 22:42:12 +0800] [374413] [INFO] Shutting down
[2025-07-26 22:42:12 +0800] [373453] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:12 +0800] [373453] [INFO] Application shutdown complete.
[2025-07-26 22:42:12 +0800] [373453] [INFO] Finished server process [373453]
[2025-07-26 22:42:12 +0800] [373453] [INFO] Worker exiting (pid: 373453)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:12 +0800] [374413] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:12 +0800] [374413] [INFO] Application shutdown complete.
[2025-07-26 22:42:12 +0800] [374413] [INFO] Finished server process [374413]
[2025-07-26 22:42:12 +0800] [374413] [INFO] Worker exiting (pid: 374413)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:12 +0800] [375840] [INFO] Booting worker with pid: 375840
[2025-07-26 22:42:12 +0800] [375840] [INFO] Started server process [375840]
[2025-07-26 22:42:12 +0800] [375840] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:42:12 +0800] [375864] [INFO] Booting worker with pid: 375864
[2025-07-26 22:42:12 +0800] [375864] [INFO] Started server process [375864]
[2025-07-26 22:42:12 +0800] [375864] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.59 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:12 +0800] [375721] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.56 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:12 +0800] [375840] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.55 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:13 +0800] [375864] [INFO] Application startup complete.
[2025-07-26 22:42:13 +0800] [374960] [WARNING] Maximum request limit of 2007 exceeded. Terminating process.
[2025-07-26 22:42:13 +0800] [374960] [INFO] Shutting down
[2025-07-26 22:42:13 +0800] [374943] [WARNING] Maximum request limit of 2006 exceeded. Terminating process.
[2025-07-26 22:42:13 +0800] [374943] [INFO] Shutting down
[2025-07-26 22:42:13 +0800] [374271] [WARNING] Maximum request limit of 2004 exceeded. Terminating process.
[2025-07-26 22:42:13 +0800] [374271] [INFO] Shutting down
[2025-07-26 22:42:13 +0800] [374960] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:13 +0800] [374960] [INFO] Application shutdown complete.
[2025-07-26 22:42:13 +0800] [374960] [INFO] Finished server process [374960]
[2025-07-26 22:42:13 +0800] [374960] [INFO] Worker exiting (pid: 374960)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:13 +0800] [374943] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:13 +0800] [374943] [INFO] Application shutdown complete.
[2025-07-26 22:42:13 +0800] [374943] [INFO] Finished server process [374943]
[2025-07-26 22:42:13 +0800] [374943] [INFO] Worker exiting (pid: 374943)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:13 +0800] [374271] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:13 +0800] [374271] [INFO] Application shutdown complete.
[2025-07-26 22:42:13 +0800] [374271] [INFO] Finished server process [374271]
[2025-07-26 22:42:13 +0800] [374271] [INFO] Worker exiting (pid: 374271)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:13 +0800] [375347] [WARNING] Maximum request limit of 2005 exceeded. Terminating process.
[2025-07-26 22:42:13 +0800] [375347] [INFO] Shutting down
[2025-07-26 22:42:13 +0800] [376313] [INFO] Booting worker with pid: 376313
[2025-07-26 22:42:13 +0800] [376313] [INFO] Started server process [376313]
[2025-07-26 22:42:13 +0800] [376313] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:42:14 +0800] [375347] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:14 +0800] [375347] [INFO] Application shutdown complete.
[2025-07-26 22:42:14 +0800] [375347] [INFO] Finished server process [375347]
[2025-07-26 22:42:14 +0800] [375347] [INFO] Worker exiting (pid: 375347)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:14 +0800] [376471] [INFO] Booting worker with pid: 376471
[2025-07-26 22:42:14 +0800] [376471] [INFO] Started server process [376471]
[2025-07-26 22:42:14 +0800] [376471] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:42:14 +0800] [376558] [INFO] Booting worker with pid: 376558
[2025-07-26 22:42:14 +0800] [376558] [INFO] Started server process [376558]
[2025-07-26 22:42:14 +0800] [376558] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:42:14 +0800] [376607] [INFO] Booting worker with pid: 376607
[2025-07-26 22:42:14 +0800] [376607] [INFO] Started server process [376607]
[2025-07-26 22:42:14 +0800] [376607] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.62 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:14 +0800] [376313] [INFO] Application startup complete.
[2025-07-26 22:42:14 +0800] [374753] [WARNING] Maximum request limit of 2022 exceeded. Terminating process.
[2025-07-26 22:42:14 +0800] [374753] [INFO] Shutting down
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.61 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:14 +0800] [376471] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.60 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:14 +0800] [376558] [INFO] Application startup complete.
[2025-07-26 22:42:14 +0800] [374753] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:14 +0800] [374753] [INFO] Application shutdown complete.
[2025-07-26 22:42:14 +0800] [374753] [INFO] Finished server process [374753]
[2025-07-26 22:42:14 +0800] [374753] [INFO] Worker exiting (pid: 374753)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.57 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:14 +0800] [376607] [INFO] Application startup complete.
[2025-07-26 22:42:14 +0800] [376879] [INFO] Booting worker with pid: 376879
[2025-07-26 22:42:14 +0800] [376879] [INFO] Started server process [376879]
[2025-07-26 22:42:14 +0800] [376879] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:42:15 +0800] [375864] [WARNING] Maximum request limit of 2003 exceeded. Terminating process.
[2025-07-26 22:42:15 +0800] [375864] [INFO] Shutting down
[2025-07-26 22:42:15 +0800] [375864] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:15 +0800] [375864] [INFO] Application shutdown complete.
[2025-07-26 22:42:15 +0800] [375864] [INFO] Finished server process [375864]
[2025-07-26 22:42:15 +0800] [375864] [INFO] Worker exiting (pid: 375864)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:15 +0800] [377019] [INFO] Booting worker with pid: 377019
[2025-07-26 22:42:15 +0800] [377019] [INFO] Started server process [377019]
[2025-07-26 22:42:15 +0800] [377019] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.54 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:15 +0800] [376879] [INFO] Application startup complete.
[2025-07-26 22:42:15 +0800] [375840] [WARNING] Maximum request limit of 2015 exceeded. Terminating process.
[2025-07-26 22:42:15 +0800] [375840] [INFO] Shutting down
[2025-07-26 22:42:15 +0800] [375840] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:15 +0800] [375840] [INFO] Application shutdown complete.
[2025-07-26 22:42:15 +0800] [375840] [INFO] Finished server process [375840]
[2025-07-26 22:42:15 +0800] [375840] [INFO] Worker exiting (pid: 375840)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.52 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:15 +0800] [377019] [INFO] Application startup complete.
[2025-07-26 22:42:15 +0800] [377066] [INFO] Booting worker with pid: 377066
[2025-07-26 22:42:15 +0800] [377066] [INFO] Started server process [377066]
[2025-07-26 22:42:15 +0800] [377066] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:42:15 +0800] [375721] [WARNING] Maximum request limit of 2048 exceeded. Terminating process.
[2025-07-26 22:42:15 +0800] [375721] [INFO] Shutting down
[2025-07-26 22:42:16 +0800] [375721] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:16 +0800] [375721] [INFO] Application shutdown complete.
[2025-07-26 22:42:16 +0800] [375721] [INFO] Finished server process [375721]
[2025-07-26 22:42:16 +0800] [375721] [INFO] Worker exiting (pid: 375721)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:16 +0800] [377355] [INFO] Booting worker with pid: 377355
[2025-07-26 22:42:16 +0800] [377355] [INFO] Started server process [377355]
[2025-07-26 22:42:16 +0800] [377355] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.56 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:16 +0800] [377066] [INFO] Application startup complete.
[2025-07-26 22:42:16 +0800] [376313] [WARNING] Maximum request limit of 2087 exceeded. Terminating process.
[2025-07-26 22:42:16 +0800] [376313] [INFO] Shutting down
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.54 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:16 +0800] [377355] [INFO] Application startup complete.
[2025-07-26 22:42:16 +0800] [376313] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:16 +0800] [376313] [INFO] Application shutdown complete.
[2025-07-26 22:42:16 +0800] [376313] [INFO] Finished server process [376313]
[2025-07-26 22:42:16 +0800] [376313] [INFO] Worker exiting (pid: 376313)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:16 +0800] [377642] [INFO] Booting worker with pid: 377642
[2025-07-26 22:42:16 +0800] [377642] [INFO] Started server process [377642]
[2025-07-26 22:42:16 +0800] [377642] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:42:17 +0800] [376607] [WARNING] Maximum request limit of 2097 exceeded. Terminating process.
[2025-07-26 22:42:17 +0800] [376607] [INFO] Shutting down
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.53 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:17 +0800] [377642] [INFO] Application startup complete.
[2025-07-26 22:42:17 +0800] [376607] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:17 +0800] [376607] [INFO] Application shutdown complete.
[2025-07-26 22:42:17 +0800] [376607] [INFO] Finished server process [376607]
[2025-07-26 22:42:17 +0800] [376607] [INFO] Worker exiting (pid: 376607)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:17 +0800] [377781] [INFO] Booting worker with pid: 377781
[2025-07-26 22:42:17 +0800] [377781] [INFO] Started server process [377781]
[2025-07-26 22:42:17 +0800] [377781] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:42:18 +0800] [377019] [WARNING] Maximum request limit of 2037 exceeded. Terminating process.
[2025-07-26 22:42:18 +0800] [377019] [INFO] Shutting down
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.52 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:18 +0800] [377781] [INFO] Application startup complete.
[2025-07-26 22:42:18 +0800] [377019] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:18 +0800] [377019] [INFO] Application shutdown complete.
[2025-07-26 22:42:18 +0800] [377019] [INFO] Finished server process [377019]
[2025-07-26 22:42:18 +0800] [377019] [INFO] Worker exiting (pid: 377019)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:18 +0800] [376879] [WARNING] Maximum request limit of 2059 exceeded. Terminating process.
[2025-07-26 22:42:18 +0800] [376879] [INFO] Shutting down
[2025-07-26 22:42:18 +0800] [378207] [INFO] Booting worker with pid: 378207
[2025-07-26 22:42:18 +0800] [378207] [INFO] Started server process [378207]
[2025-07-26 22:42:18 +0800] [378207] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:42:18 +0800] [376879] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:18 +0800] [376879] [INFO] Application shutdown complete.
[2025-07-26 22:42:18 +0800] [376879] [INFO] Finished server process [376879]
[2025-07-26 22:42:18 +0800] [376879] [INFO] Worker exiting (pid: 376879)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:18 +0800] [376558] [WARNING] Maximum request limit of 2100 exceeded. Terminating process.
[2025-07-26 22:42:18 +0800] [376558] [INFO] Shutting down
[2025-07-26 22:42:18 +0800] [378376] [INFO] Booting worker with pid: 378376
[2025-07-26 22:42:18 +0800] [378376] [INFO] Started server process [378376]
[2025-07-26 22:42:18 +0800] [378376] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:42:18 +0800] [376558] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:18 +0800] [376558] [INFO] Application shutdown complete.
[2025-07-26 22:42:18 +0800] [376558] [INFO] Finished server process [376558]
[2025-07-26 22:42:18 +0800] [376558] [INFO] Worker exiting (pid: 376558)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:18 +0800] [378394] [INFO] Booting worker with pid: 378394
[2025-07-26 22:42:18 +0800] [378394] [INFO] Started server process [378394]
[2025-07-26 22:42:18 +0800] [378394] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:42:18 +0800] [376471] [WARNING] Maximum request limit of 2043 exceeded. Terminating process.
[2025-07-26 22:42:18 +0800] [376471] [INFO] Shutting down
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.59 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:18 +0800] [378207] [INFO] Application startup complete.
[2025-07-26 22:42:19 +0800] [376471] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:19 +0800] [376471] [INFO] Application shutdown complete.
[2025-07-26 22:42:19 +0800] [376471] [INFO] Finished server process [376471]
[2025-07-26 22:42:19 +0800] [376471] [INFO] Worker exiting (pid: 376471)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:19 +0800] [378528] [INFO] Booting worker with pid: 378528
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.57 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:19 +0800] [378376] [INFO] Application startup complete.
[2025-07-26 22:42:19 +0800] [378528] [INFO] Started server process [378528]
[2025-07-26 22:42:19 +0800] [378528] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:42:19 +0800] [377066] [WARNING] Maximum request limit of 2031 exceeded. Terminating process.
[2025-07-26 22:42:19 +0800] [377066] [INFO] Shutting down
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.58 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:19 +0800] [378394] [INFO] Application startup complete.
[2025-07-26 22:42:19 +0800] [377066] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:19 +0800] [377066] [INFO] Application shutdown complete.
[2025-07-26 22:42:19 +0800] [377066] [INFO] Finished server process [377066]
[2025-07-26 22:42:19 +0800] [377066] [INFO] Worker exiting (pid: 377066)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:19 +0800] [378666] [INFO] Booting worker with pid: 378666
[2025-07-26 22:42:19 +0800] [378666] [INFO] Started server process [378666]
[2025-07-26 22:42:19 +0800] [378666] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:42:19 +0800] [377355] [WARNING] Maximum request limit of 2053 exceeded. Terminating process.
[2025-07-26 22:42:19 +0800] [377355] [INFO] Shutting down
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.58 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:19 +0800] [378528] [INFO] Application startup complete.
[2025-07-26 22:42:19 +0800] [377355] [INFO] Waiting for application shutdown.
[2025-07-26 22:42:19 +0800] [377355] [INFO] Application shutdown complete.
[2025-07-26 22:42:19 +0800] [377355] [INFO] Finished server process [377355]
[2025-07-26 22:42:19 +0800] [377355] [INFO] Worker exiting (pid: 377355)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:42:19 +0800] [378695] [INFO] Booting worker with pid: 378695
[2025-07-26 22:42:19 +0800] [378695] [INFO] Started server process [378695]
[2025-07-26 22:42:19 +0800] [378695] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.55 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:20 +0800] [378666] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.53 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:42:20 +0800] [378695] [INFO] Application startup complete.
[2025-07-26 22:43:14 +0800] [377642] [WARNING] Maximum request limit of 2036 exceeded. Terminating process.
[2025-07-26 22:43:14 +0800] [377642] [INFO] Shutting down
[2025-07-26 22:43:14 +0800] [377642] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:14 +0800] [377642] [INFO] Application shutdown complete.
[2025-07-26 22:43:14 +0800] [377642] [INFO] Finished server process [377642]
[2025-07-26 22:43:14 +0800] [377642] [INFO] Worker exiting (pid: 377642)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:14 +0800] [403585] [INFO] Booting worker with pid: 403585
[2025-07-26 22:43:14 +0800] [403585] [INFO] Started server process [403585]
[2025-07-26 22:43:14 +0800] [403585] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:14 +0800] [378376] [WARNING] Maximum request limit of 2080 exceeded. Terminating process.
[2025-07-26 22:43:14 +0800] [378376] [INFO] Shutting down
[2025-07-26 22:43:15 +0800] [378376] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:15 +0800] [378376] [INFO] Application shutdown complete.
[2025-07-26 22:43:15 +0800] [378376] [INFO] Finished server process [378376]
[2025-07-26 22:43:15 +0800] [378376] [INFO] Worker exiting (pid: 378376)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:15 +0800] [377781] [WARNING] Maximum request limit of 2044 exceeded. Terminating process.
[2025-07-26 22:43:15 +0800] [377781] [INFO] Shutting down
[2025-07-26 22:43:15 +0800] [377781] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:15 +0800] [377781] [INFO] Application shutdown complete.
[2025-07-26 22:43:15 +0800] [377781] [INFO] Finished server process [377781]
[2025-07-26 22:43:15 +0800] [377781] [INFO] Worker exiting (pid: 377781)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:15 +0800] [403625] [INFO] Booting worker with pid: 403625
[2025-07-26 22:43:15 +0800] [403625] [INFO] Started server process [403625]
[2025-07-26 22:43:15 +0800] [403625] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:15 +0800] [378666] [WARNING] Maximum request limit of 2015 exceeded. Terminating process.
[2025-07-26 22:43:15 +0800] [378666] [INFO] Shutting down
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.54 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:15 +0800] [403585] [INFO] Application startup complete.
[2025-07-26 22:43:15 +0800] [378666] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:15 +0800] [378666] [INFO] Application shutdown complete.
[2025-07-26 22:43:15 +0800] [378666] [INFO] Finished server process [378666]
[2025-07-26 22:43:15 +0800] [378666] [INFO] Worker exiting (pid: 378666)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:15 +0800] [403627] [INFO] Booting worker with pid: 403627
[2025-07-26 22:43:15 +0800] [403627] [INFO] Started server process [403627]
[2025-07-26 22:43:15 +0800] [403627] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:15 +0800] [403628] [INFO] Booting worker with pid: 403628
[2025-07-26 22:43:15 +0800] [378394] [WARNING] Maximum request limit of 2071 exceeded. Terminating process.
[2025-07-26 22:43:15 +0800] [378394] [INFO] Shutting down
[2025-07-26 22:43:15 +0800] [403628] [INFO] Started server process [403628]
[2025-07-26 22:43:15 +0800] [403628] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:15 +0800] [378394] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:15 +0800] [378394] [INFO] Application shutdown complete.
[2025-07-26 22:43:15 +0800] [378394] [INFO] Finished server process [378394]
[2025-07-26 22:43:15 +0800] [378394] [INFO] Worker exiting (pid: 378394)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:15 +0800] [378528] [WARNING] Maximum request limit of 2088 exceeded. Terminating process.
[2025-07-26 22:43:15 +0800] [378528] [INFO] Shutting down
[2025-07-26 22:43:15 +0800] [378695] [WARNING] Maximum request limit of 2096 exceeded. Terminating process.
[2025-07-26 22:43:15 +0800] [378695] [INFO] Shutting down
[2025-07-26 22:43:15 +0800] [378528] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:15 +0800] [378528] [INFO] Application shutdown complete.
[2025-07-26 22:43:15 +0800] [378528] [INFO] Finished server process [378528]
[2025-07-26 22:43:15 +0800] [378528] [INFO] Worker exiting (pid: 378528)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:15 +0800] [403629] [INFO] Booting worker with pid: 403629
[2025-07-26 22:43:15 +0800] [403629] [INFO] Started server process [403629]
[2025-07-26 22:43:15 +0800] [403629] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:15 +0800] [378695] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:15 +0800] [378695] [INFO] Application shutdown complete.
[2025-07-26 22:43:15 +0800] [378695] [INFO] Finished server process [378695]
[2025-07-26 22:43:15 +0800] [378695] [INFO] Worker exiting (pid: 378695)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.64 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:15 +0800] [403625] [INFO] Application startup complete.
[2025-07-26 22:43:15 +0800] [403667] [INFO] Booting worker with pid: 403667
[2025-07-26 22:43:15 +0800] [403667] [INFO] Started server process [403667]
[2025-07-26 22:43:15 +0800] [403667] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:15 +0800] [403674] [INFO] Booting worker with pid: 403674
[2025-07-26 22:43:15 +0800] [378207] [WARNING] Maximum request limit of 2035 exceeded. Terminating process.
[2025-07-26 22:43:15 +0800] [378207] [INFO] Shutting down
[2025-07-26 22:43:15 +0800] [403674] [INFO] Started server process [403674]
[2025-07-26 22:43:15 +0800] [403674] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.65 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:15 +0800] [403627] [INFO] Application startup complete.
[2025-07-26 22:43:16 +0800] [378207] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:16 +0800] [378207] [INFO] Application shutdown complete.
[2025-07-26 22:43:16 +0800] [378207] [INFO] Finished server process [378207]
[2025-07-26 22:43:16 +0800] [378207] [INFO] Worker exiting (pid: 378207)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.66 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:16 +0800] [403628] [INFO] Application startup complete.
[2025-07-26 22:43:16 +0800] [403951] [INFO] Booting worker with pid: 403951
[2025-07-26 22:43:16 +0800] [403951] [INFO] Started server process [403951]
[2025-07-26 22:43:16 +0800] [403951] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.63 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:16 +0800] [403629] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.60 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:16 +0800] [403667] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.60 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:16 +0800] [403674] [INFO] Application startup complete.
[2025-07-26 22:43:16 +0800] [403585] [WARNING] Maximum request limit of 2090 exceeded. Terminating process.
[2025-07-26 22:43:16 +0800] [403585] [INFO] Shutting down
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.55 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:16 +0800] [403951] [INFO] Application startup complete.
[2025-07-26 22:43:16 +0800] [403585] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:16 +0800] [403585] [INFO] Application shutdown complete.
[2025-07-26 22:43:16 +0800] [403585] [INFO] Finished server process [403585]
[2025-07-26 22:43:16 +0800] [403585] [INFO] Worker exiting (pid: 403585)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:17 +0800] [404301] [INFO] Booting worker with pid: 404301
[2025-07-26 22:43:17 +0800] [404301] [INFO] Started server process [404301]
[2025-07-26 22:43:17 +0800] [404301] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:17 +0800] [403625] [WARNING] Maximum request limit of 2049 exceeded. Terminating process.
[2025-07-26 22:43:17 +0800] [403625] [INFO] Shutting down
[2025-07-26 22:43:17 +0800] [403625] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:17 +0800] [403625] [INFO] Application shutdown complete.
[2025-07-26 22:43:17 +0800] [403625] [INFO] Finished server process [403625]
[2025-07-26 22:43:17 +0800] [403625] [INFO] Worker exiting (pid: 403625)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:17 +0800] [404331] [INFO] Booting worker with pid: 404331
[2025-07-26 22:43:17 +0800] [404331] [INFO] Started server process [404331]
[2025-07-26 22:43:17 +0800] [404331] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.54 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:17 +0800] [404301] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.52 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:17 +0800] [404331] [INFO] Application startup complete.
[2025-07-26 22:43:18 +0800] [403667] [WARNING] Maximum request limit of 2031 exceeded. Terminating process.
[2025-07-26 22:43:18 +0800] [403667] [INFO] Shutting down
[2025-07-26 22:43:18 +0800] [403627] [WARNING] Maximum request limit of 2066 exceeded. Terminating process.
[2025-07-26 22:43:18 +0800] [403627] [INFO] Shutting down
[2025-07-26 22:43:18 +0800] [403667] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:18 +0800] [403667] [INFO] Application shutdown complete.
[2025-07-26 22:43:18 +0800] [403667] [INFO] Finished server process [403667]
[2025-07-26 22:43:18 +0800] [403667] [INFO] Worker exiting (pid: 403667)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:18 +0800] [403627] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:18 +0800] [403627] [INFO] Application shutdown complete.
[2025-07-26 22:43:18 +0800] [403627] [INFO] Finished server process [403627]
[2025-07-26 22:43:18 +0800] [403627] [INFO] Worker exiting (pid: 403627)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:18 +0800] [403628] [WARNING] Maximum request limit of 2080 exceeded. Terminating process.
[2025-07-26 22:43:18 +0800] [403628] [INFO] Shutting down
[2025-07-26 22:43:18 +0800] [405128] [INFO] Booting worker with pid: 405128
[2025-07-26 22:43:18 +0800] [405128] [INFO] Started server process [405128]
[2025-07-26 22:43:18 +0800] [405128] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:18 +0800] [403951] [WARNING] Maximum request limit of 2032 exceeded. Terminating process.
[2025-07-26 22:43:18 +0800] [403951] [INFO] Shutting down
[2025-07-26 22:43:18 +0800] [403628] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:18 +0800] [403628] [INFO] Application shutdown complete.
[2025-07-26 22:43:18 +0800] [403628] [INFO] Finished server process [403628]
[2025-07-26 22:43:18 +0800] [403628] [INFO] Worker exiting (pid: 403628)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:18 +0800] [405148] [INFO] Booting worker with pid: 405148
[2025-07-26 22:43:18 +0800] [405148] [INFO] Started server process [405148]
[2025-07-26 22:43:18 +0800] [405148] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:18 +0800] [403951] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:18 +0800] [403951] [INFO] Application shutdown complete.
[2025-07-26 22:43:18 +0800] [403951] [INFO] Finished server process [403951]
[2025-07-26 22:43:18 +0800] [403951] [INFO] Worker exiting (pid: 403951)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:18 +0800] [403674] [WARNING] Maximum request limit of 2021 exceeded. Terminating process.
[2025-07-26 22:43:18 +0800] [403674] [INFO] Shutting down
[2025-07-26 22:43:18 +0800] [403629] [WARNING] Maximum request limit of 2089 exceeded. Terminating process.
[2025-07-26 22:43:18 +0800] [403629] [INFO] Shutting down
[2025-07-26 22:43:18 +0800] [405256] [INFO] Booting worker with pid: 405256
[2025-07-26 22:43:18 +0800] [405256] [INFO] Started server process [405256]
[2025-07-26 22:43:18 +0800] [405256] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:18 +0800] [403674] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:18 +0800] [403674] [INFO] Application shutdown complete.
[2025-07-26 22:43:18 +0800] [403674] [INFO] Finished server process [403674]
[2025-07-26 22:43:18 +0800] [403674] [INFO] Worker exiting (pid: 403674)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:18 +0800] [403629] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:18 +0800] [403629] [INFO] Application shutdown complete.
[2025-07-26 22:43:18 +0800] [403629] [INFO] Finished server process [403629]
[2025-07-26 22:43:18 +0800] [403629] [INFO] Worker exiting (pid: 403629)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:18 +0800] [405257] [INFO] Booting worker with pid: 405257
[2025-07-26 22:43:18 +0800] [405257] [INFO] Started server process [405257]
[2025-07-26 22:43:18 +0800] [405257] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:18 +0800] [405290] [INFO] Booting worker with pid: 405290
[2025-07-26 22:43:19 +0800] [405290] [INFO] Started server process [405290]
[2025-07-26 22:43:19 +0800] [405290] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:19 +0800] [405408] [INFO] Booting worker with pid: 405408
[2025-07-26 22:43:19 +0800] [405408] [INFO] Started server process [405408]
[2025-07-26 22:43:19 +0800] [405408] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.70 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:19 +0800] [405128] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.71 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:19 +0800] [405148] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.66 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:19 +0800] [405256] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.63 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:19 +0800] [405257] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.59 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:19 +0800] [405290] [INFO] Application startup complete.
[2025-07-26 22:43:19 +0800] [404331] [WARNING] Maximum request limit of 2090 exceeded. Terminating process.
[2025-07-26 22:43:19 +0800] [404331] [INFO] Shutting down
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.57 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:19 +0800] [405408] [INFO] Application startup complete.
[2025-07-26 22:43:19 +0800] [404331] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:19 +0800] [404331] [INFO] Application shutdown complete.
[2025-07-26 22:43:19 +0800] [404331] [INFO] Finished server process [404331]
[2025-07-26 22:43:19 +0800] [404331] [INFO] Worker exiting (pid: 404331)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:19 +0800] [404301] [WARNING] Maximum request limit of 2087 exceeded. Terminating process.
[2025-07-26 22:43:19 +0800] [404301] [INFO] Shutting down
[2025-07-26 22:43:19 +0800] [404301] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:19 +0800] [404301] [INFO] Application shutdown complete.
[2025-07-26 22:43:19 +0800] [404301] [INFO] Finished server process [404301]
[2025-07-26 22:43:19 +0800] [404301] [INFO] Worker exiting (pid: 404301)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:19 +0800] [405768] [INFO] Booting worker with pid: 405768
[2025-07-26 22:43:19 +0800] [405768] [INFO] Started server process [405768]
[2025-07-26 22:43:19 +0800] [405768] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:19 +0800] [405896] [INFO] Booting worker with pid: 405896
[2025-07-26 22:43:20 +0800] [405896] [INFO] Started server process [405896]
[2025-07-26 22:43:20 +0800] [405896] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.57 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:20 +0800] [405768] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.54 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:20 +0800] [405896] [INFO] Application startup complete.
[2025-07-26 22:43:21 +0800] [405257] [WARNING] Maximum request limit of 2012 exceeded. Terminating process.
[2025-07-26 22:43:21 +0800] [405257] [INFO] Shutting down
[2025-07-26 22:43:21 +0800] [405256] [WARNING] Maximum request limit of 2038 exceeded. Terminating process.
[2025-07-26 22:43:21 +0800] [405256] [INFO] Shutting down
[2025-07-26 22:43:21 +0800] [405257] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:21 +0800] [405257] [INFO] Application shutdown complete.
[2025-07-26 22:43:21 +0800] [405257] [INFO] Finished server process [405257]
[2025-07-26 22:43:21 +0800] [405257] [INFO] Worker exiting (pid: 405257)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:21 +0800] [405128] [WARNING] Maximum request limit of 2066 exceeded. Terminating process.
[2025-07-26 22:43:21 +0800] [405128] [INFO] Shutting down
[2025-07-26 22:43:21 +0800] [405256] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:21 +0800] [405256] [INFO] Application shutdown complete.
[2025-07-26 22:43:21 +0800] [405256] [INFO] Finished server process [405256]
[2025-07-26 22:43:21 +0800] [405256] [INFO] Worker exiting (pid: 405256)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:21 +0800] [406976] [INFO] Booting worker with pid: 406976
[2025-07-26 22:43:21 +0800] [405128] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:21 +0800] [405128] [INFO] Application shutdown complete.
[2025-07-26 22:43:21 +0800] [405128] [INFO] Finished server process [405128]
[2025-07-26 22:43:21 +0800] [405128] [INFO] Worker exiting (pid: 405128)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:21 +0800] [406976] [INFO] Started server process [406976]
[2025-07-26 22:43:21 +0800] [406976] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:21 +0800] [406980] [INFO] Booting worker with pid: 406980
[2025-07-26 22:43:21 +0800] [406980] [INFO] Started server process [406980]
[2025-07-26 22:43:21 +0800] [406980] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:21 +0800] [407020] [INFO] Booting worker with pid: 407020
[2025-07-26 22:43:21 +0800] [407020] [INFO] Started server process [407020]
[2025-07-26 22:43:21 +0800] [407020] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:21 +0800] [405148] [WARNING] Maximum request limit of 2075 exceeded. Terminating process.
[2025-07-26 22:43:21 +0800] [405148] [INFO] Shutting down
[2025-07-26 22:43:21 +0800] [405290] [WARNING] Maximum request limit of 2071 exceeded. Terminating process.
[2025-07-26 22:43:21 +0800] [405290] [INFO] Shutting down
[2025-07-26 22:43:21 +0800] [405148] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:21 +0800] [405148] [INFO] Application shutdown complete.
[2025-07-26 22:43:21 +0800] [405148] [INFO] Finished server process [405148]
[2025-07-26 22:43:21 +0800] [405148] [INFO] Worker exiting (pid: 405148)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:21 +0800] [405290] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:21 +0800] [405290] [INFO] Application shutdown complete.
[2025-07-26 22:43:21 +0800] [405290] [INFO] Finished server process [405290]
[2025-07-26 22:43:21 +0800] [405290] [INFO] Worker exiting (pid: 405290)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:21 +0800] [407119] [INFO] Booting worker with pid: 407119
[2025-07-26 22:43:21 +0800] [407119] [INFO] Started server process [407119]
[2025-07-26 22:43:21 +0800] [407119] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.59 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:21 +0800] [406976] [INFO] Application startup complete.
[2025-07-26 22:43:21 +0800] [407120] [INFO] Booting worker with pid: 407120
[2025-07-26 22:43:22 +0800] [407120] [INFO] Started server process [407120]
[2025-07-26 22:43:22 +0800] [407120] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.61 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:22 +0800] [406980] [INFO] Application startup complete.
[2025-07-26 22:43:22 +0800] [405408] [WARNING] Maximum request limit of 2033 exceeded. Terminating process.
[2025-07-26 22:43:22 +0800] [405408] [INFO] Shutting down
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.59 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:22 +0800] [407020] [INFO] Application startup complete.
[2025-07-26 22:43:22 +0800] [405408] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:22 +0800] [405408] [INFO] Application shutdown complete.
[2025-07-26 22:43:22 +0800] [405408] [INFO] Finished server process [405408]
[2025-07-26 22:43:22 +0800] [405408] [INFO] Worker exiting (pid: 405408)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:22 +0800] [407323] [INFO] Booting worker with pid: 407323
[2025-07-26 22:43:22 +0800] [407323] [INFO] Started server process [407323]
[2025-07-26 22:43:22 +0800] [407323] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:22 +0800] [405768] [WARNING] Maximum request limit of 2002 exceeded. Terminating process.
[2025-07-26 22:43:22 +0800] [405768] [INFO] Shutting down
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.59 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:22 +0800] [407119] [INFO] Application startup complete.
[2025-07-26 22:43:22 +0800] [405768] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:22 +0800] [405768] [INFO] Application shutdown complete.
[2025-07-26 22:43:22 +0800] [405768] [INFO] Finished server process [405768]
[2025-07-26 22:43:22 +0800] [405768] [INFO] Worker exiting (pid: 405768)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.59 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:22 +0800] [407120] [INFO] Application startup complete.
[2025-07-26 22:43:22 +0800] [405896] [WARNING] Maximum request limit of 2099 exceeded. Terminating process.
[2025-07-26 22:43:22 +0800] [405896] [INFO] Shutting down
[2025-07-26 22:43:22 +0800] [407366] [INFO] Booting worker with pid: 407366
[2025-07-26 22:43:22 +0800] [407366] [INFO] Started server process [407366]
[2025-07-26 22:43:22 +0800] [407366] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:22 +0800] [405896] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:22 +0800] [405896] [INFO] Application shutdown complete.
[2025-07-26 22:43:22 +0800] [405896] [INFO] Finished server process [405896]
[2025-07-26 22:43:22 +0800] [405896] [INFO] Worker exiting (pid: 405896)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.57 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:22 +0800] [407323] [INFO] Application startup complete.
[2025-07-26 22:43:22 +0800] [407376] [INFO] Booting worker with pid: 407376
[2025-07-26 22:43:22 +0800] [407376] [INFO] Started server process [407376]
[2025-07-26 22:43:22 +0800] [407376] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.56 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:23 +0800] [407366] [INFO] Application startup complete.
[2025-07-26 22:43:23 +0800] [406976] [WARNING] Maximum request limit of 2063 exceeded. Terminating process.
[2025-07-26 22:43:23 +0800] [406976] [INFO] Shutting down
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.53 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:23 +0800] [407376] [INFO] Application startup complete.
[2025-07-26 22:43:23 +0800] [406976] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:23 +0800] [406976] [INFO] Application shutdown complete.
[2025-07-26 22:43:23 +0800] [406976] [INFO] Finished server process [406976]
[2025-07-26 22:43:23 +0800] [406976] [INFO] Worker exiting (pid: 406976)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:23 +0800] [406980] [WARNING] Maximum request limit of 2018 exceeded. Terminating process.
[2025-07-26 22:43:23 +0800] [406980] [INFO] Shutting down
[2025-07-26 22:43:23 +0800] [407795] [INFO] Booting worker with pid: 407795
[2025-07-26 22:43:23 +0800] [407795] [INFO] Started server process [407795]
[2025-07-26 22:43:23 +0800] [407795] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:23 +0800] [406980] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:23 +0800] [406980] [INFO] Application shutdown complete.
[2025-07-26 22:43:23 +0800] [406980] [INFO] Finished server process [406980]
[2025-07-26 22:43:23 +0800] [406980] [INFO] Worker exiting (pid: 406980)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:23 +0800] [407836] [INFO] Booting worker with pid: 407836
[2025-07-26 22:43:23 +0800] [407836] [INFO] Started server process [407836]
[2025-07-26 22:43:23 +0800] [407836] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.56 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:24 +0800] [407795] [INFO] Application startup complete.
[2025-07-26 22:43:24 +0800] [407119] [WARNING] Maximum request limit of 2038 exceeded. Terminating process.
[2025-07-26 22:43:24 +0800] [407119] [INFO] Shutting down
[2025-07-26 22:43:24 +0800] [407020] [WARNING] Maximum request limit of 2044 exceeded. Terminating process.
[2025-07-26 22:43:24 +0800] [407020] [INFO] Shutting down
[2025-07-26 22:43:24 +0800] [407119] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:24 +0800] [407119] [INFO] Application shutdown complete.
[2025-07-26 22:43:24 +0800] [407119] [INFO] Finished server process [407119]
[2025-07-26 22:43:24 +0800] [407119] [INFO] Worker exiting (pid: 407119)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:24 +0800] [407020] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:24 +0800] [407020] [INFO] Application shutdown complete.
[2025-07-26 22:43:24 +0800] [407020] [INFO] Finished server process [407020]
[2025-07-26 22:43:24 +0800] [407020] [INFO] Worker exiting (pid: 407020)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.54 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:24 +0800] [407836] [INFO] Application startup complete.
[2025-07-26 22:43:24 +0800] [408199] [INFO] Booting worker with pid: 408199
[2025-07-26 22:43:24 +0800] [408199] [INFO] Started server process [408199]
[2025-07-26 22:43:24 +0800] [408199] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:24 +0800] [407120] [WARNING] Maximum request limit of 2000 exceeded. Terminating process.
[2025-07-26 22:43:24 +0800] [407120] [INFO] Shutting down
[2025-07-26 22:43:24 +0800] [408228] [INFO] Booting worker with pid: 408228
[2025-07-26 22:43:24 +0800] [408228] [INFO] Started server process [408228]
[2025-07-26 22:43:24 +0800] [408228] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:24 +0800] [407120] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:24 +0800] [407120] [INFO] Application shutdown complete.
[2025-07-26 22:43:24 +0800] [407120] [INFO] Finished server process [407120]
[2025-07-26 22:43:24 +0800] [407120] [INFO] Worker exiting (pid: 407120)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:24 +0800] [408333] [INFO] Booting worker with pid: 408333
[2025-07-26 22:43:24 +0800] [408333] [INFO] Started server process [408333]
[2025-07-26 22:43:24 +0800] [408333] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:24 +0800] [407323] [WARNING] Maximum request limit of 2006 exceeded. Terminating process.
[2025-07-26 22:43:24 +0800] [407323] [INFO] Shutting down
[2025-07-26 22:43:25 +0800] [407323] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:25 +0800] [407323] [INFO] Application shutdown complete.
[2025-07-26 22:43:25 +0800] [407323] [INFO] Finished server process [407323]
[2025-07-26 22:43:25 +0800] [407323] [INFO] Worker exiting (pid: 407323)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.59 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:25 +0800] [408199] [INFO] Application startup complete.
[2025-07-26 22:43:25 +0800] [408588] [INFO] Booting worker with pid: 408588
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.59 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:25 +0800] [408228] [INFO] Application startup complete.
[2025-07-26 22:43:25 +0800] [408588] [INFO] Started server process [408588]
[2025-07-26 22:43:25 +0800] [408588] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:25 +0800] [407366] [WARNING] Maximum request limit of 2045 exceeded. Terminating process.
[2025-07-26 22:43:25 +0800] [407366] [INFO] Shutting down
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.59 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:25 +0800] [408333] [INFO] Application startup complete.
[2025-07-26 22:43:25 +0800] [407366] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:25 +0800] [407366] [INFO] Application shutdown complete.
[2025-07-26 22:43:25 +0800] [407366] [INFO] Finished server process [407366]
[2025-07-26 22:43:25 +0800] [407366] [INFO] Worker exiting (pid: 407366)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:25 +0800] [408626] [INFO] Booting worker with pid: 408626
[2025-07-26 22:43:25 +0800] [408626] [INFO] Started server process [408626]
[2025-07-26 22:43:25 +0800] [408626] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:25 +0800] [407795] [WARNING] Maximum request limit of 2072 exceeded. Terminating process.
[2025-07-26 22:43:25 +0800] [407795] [INFO] Shutting down
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.57 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:25 +0800] [408588] [INFO] Application startup complete.
[2025-07-26 22:43:25 +0800] [407376] [WARNING] Maximum request limit of 2037 exceeded. Terminating process.
[2025-07-26 22:43:25 +0800] [407376] [INFO] Shutting down
[2025-07-26 22:43:25 +0800] [407795] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:25 +0800] [407795] [INFO] Application shutdown complete.
[2025-07-26 22:43:25 +0800] [407795] [INFO] Finished server process [407795]
[2025-07-26 22:43:25 +0800] [407795] [INFO] Worker exiting (pid: 407795)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:25 +0800] [407376] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:25 +0800] [407376] [INFO] Application shutdown complete.
[2025-07-26 22:43:25 +0800] [407376] [INFO] Finished server process [407376]
[2025-07-26 22:43:25 +0800] [407376] [INFO] Worker exiting (pid: 407376)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:25 +0800] [408713] [INFO] Booting worker with pid: 408713
[2025-07-26 22:43:25 +0800] [408713] [INFO] Started server process [408713]
[2025-07-26 22:43:25 +0800] [408713] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:26 +0800] [408750] [INFO] Booting worker with pid: 408750
[2025-07-26 22:43:26 +0800] [407836] [WARNING] Maximum request limit of 2059 exceeded. Terminating process.
[2025-07-26 22:43:26 +0800] [407836] [INFO] Shutting down
[2025-07-26 22:43:26 +0800] [408750] [INFO] Started server process [408750]
[2025-07-26 22:43:26 +0800] [408750] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:26 +0800] [407836] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:26 +0800] [407836] [INFO] Application shutdown complete.
[2025-07-26 22:43:26 +0800] [407836] [INFO] Finished server process [407836]
[2025-07-26 22:43:26 +0800] [407836] [INFO] Worker exiting (pid: 407836)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.59 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:26 +0800] [408626] [INFO] Application startup complete.
[2025-07-26 22:43:26 +0800] [408778] [INFO] Booting worker with pid: 408778
[2025-07-26 22:43:26 +0800] [408778] [INFO] Started server process [408778]
[2025-07-26 22:43:26 +0800] [408778] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.59 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:26 +0800] [408713] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.59 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:26 +0800] [408750] [INFO] Application startup complete.
[2025-07-26 22:43:26 +0800] [408199] [WARNING] Maximum request limit of 2091 exceeded. Terminating process.
[2025-07-26 22:43:26 +0800] [408199] [INFO] Shutting down
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.54 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:26 +0800] [408778] [INFO] Application startup complete.
[2025-07-26 22:43:26 +0800] [408228] [WARNING] Maximum request limit of 2025 exceeded. Terminating process.
[2025-07-26 22:43:26 +0800] [408228] [INFO] Shutting down
[2025-07-26 22:43:26 +0800] [408199] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:26 +0800] [408199] [INFO] Application shutdown complete.
[2025-07-26 22:43:26 +0800] [408199] [INFO] Finished server process [408199]
[2025-07-26 22:43:26 +0800] [408199] [INFO] Worker exiting (pid: 408199)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:27 +0800] [408228] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:27 +0800] [408228] [INFO] Application shutdown complete.
[2025-07-26 22:43:27 +0800] [408228] [INFO] Finished server process [408228]
[2025-07-26 22:43:27 +0800] [408228] [INFO] Worker exiting (pid: 408228)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:27 +0800] [409106] [INFO] Booting worker with pid: 409106
[2025-07-26 22:43:27 +0800] [408333] [WARNING] Maximum request limit of 2016 exceeded. Terminating process.
[2025-07-26 22:43:27 +0800] [408333] [INFO] Shutting down
[2025-07-26 22:43:27 +0800] [409106] [INFO] Started server process [409106]
[2025-07-26 22:43:27 +0800] [409106] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:27 +0800] [409205] [INFO] Booting worker with pid: 409205
[2025-07-26 22:43:27 +0800] [408333] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:27 +0800] [408333] [INFO] Application shutdown complete.
[2025-07-26 22:43:27 +0800] [408333] [INFO] Finished server process [408333]
[2025-07-26 22:43:27 +0800] [408333] [INFO] Worker exiting (pid: 408333)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:27 +0800] [409205] [INFO] Started server process [409205]
[2025-07-26 22:43:27 +0800] [409205] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:27 +0800] [408588] [WARNING] Maximum request limit of 2023 exceeded. Terminating process.
[2025-07-26 22:43:27 +0800] [408588] [INFO] Shutting down
[2025-07-26 22:43:27 +0800] [409213] [INFO] Booting worker with pid: 409213
[2025-07-26 22:43:27 +0800] [409213] [INFO] Started server process [409213]
[2025-07-26 22:43:27 +0800] [409213] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:27 +0800] [408588] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:27 +0800] [408588] [INFO] Application shutdown complete.
[2025-07-26 22:43:27 +0800] [408588] [INFO] Finished server process [408588]
[2025-07-26 22:43:27 +0800] [408588] [INFO] Worker exiting (pid: 408588)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:27 +0800] [409247] [INFO] Booting worker with pid: 409247
[2025-07-26 22:43:27 +0800] [409247] [INFO] Started server process [409247]
[2025-07-26 22:43:27 +0800] [409247] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.62 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:27 +0800] [409106] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.63 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:27 +0800] [409205] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.60 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:28 +0800] [409213] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.56 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:28 +0800] [409247] [INFO] Application startup complete.
[2025-07-26 22:43:28 +0800] [408626] [WARNING] Maximum request limit of 2071 exceeded. Terminating process.
[2025-07-26 22:43:28 +0800] [408626] [INFO] Shutting down
[2025-07-26 22:43:28 +0800] [408750] [WARNING] Maximum request limit of 2095 exceeded. Terminating process.
[2025-07-26 22:43:28 +0800] [408750] [INFO] Shutting down
[2025-07-26 22:43:28 +0800] [408626] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:28 +0800] [408626] [INFO] Application shutdown complete.
[2025-07-26 22:43:28 +0800] [408626] [INFO] Finished server process [408626]
[2025-07-26 22:43:28 +0800] [408626] [INFO] Worker exiting (pid: 408626)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:28 +0800] [408750] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:28 +0800] [408750] [INFO] Application shutdown complete.
[2025-07-26 22:43:28 +0800] [408750] [INFO] Finished server process [408750]
[2025-07-26 22:43:28 +0800] [408750] [INFO] Worker exiting (pid: 408750)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:28 +0800] [408778] [WARNING] Maximum request limit of 2025 exceeded. Terminating process.
[2025-07-26 22:43:28 +0800] [408778] [INFO] Shutting down
[2025-07-26 22:43:28 +0800] [409775] [INFO] Booting worker with pid: 409775
[2025-07-26 22:43:28 +0800] [408713] [WARNING] Maximum request limit of 2041 exceeded. Terminating process.
[2025-07-26 22:43:28 +0800] [408713] [INFO] Shutting down
[2025-07-26 22:43:28 +0800] [409775] [INFO] Started server process [409775]
[2025-07-26 22:43:28 +0800] [409775] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:28 +0800] [408778] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:28 +0800] [408778] [INFO] Application shutdown complete.
[2025-07-26 22:43:28 +0800] [408778] [INFO] Finished server process [408778]
[2025-07-26 22:43:28 +0800] [408778] [INFO] Worker exiting (pid: 408778)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:28 +0800] [409807] [INFO] Booting worker with pid: 409807
[2025-07-26 22:43:28 +0800] [408713] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:28 +0800] [408713] [INFO] Application shutdown complete.
[2025-07-26 22:43:28 +0800] [408713] [INFO] Finished server process [408713]
[2025-07-26 22:43:28 +0800] [408713] [INFO] Worker exiting (pid: 408713)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:28 +0800] [409807] [INFO] Started server process [409807]
[2025-07-26 22:43:28 +0800] [409807] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:28 +0800] [409836] [INFO] Booting worker with pid: 409836
[2025-07-26 22:43:28 +0800] [409836] [INFO] Started server process [409836]
[2025-07-26 22:43:28 +0800] [409836] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:28 +0800] [409893] [INFO] Booting worker with pid: 409893
[2025-07-26 22:43:28 +0800] [409893] [INFO] Started server process [409893]
[2025-07-26 22:43:28 +0800] [409893] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.62 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:29 +0800] [409775] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.61 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:29 +0800] [409807] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.59 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:29 +0800] [409836] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.57 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:29 +0800] [409893] [INFO] Application startup complete.
[2025-07-26 22:43:29 +0800] [409213] [WARNING] Maximum request limit of 2020 exceeded. Terminating process.
[2025-07-26 22:43:29 +0800] [409213] [INFO] Shutting down
[2025-07-26 22:43:29 +0800] [409213] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:29 +0800] [409213] [INFO] Application shutdown complete.
[2025-07-26 22:43:29 +0800] [409213] [INFO] Finished server process [409213]
[2025-07-26 22:43:29 +0800] [409213] [INFO] Worker exiting (pid: 409213)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:29 +0800] [409205] [WARNING] Maximum request limit of 2033 exceeded. Terminating process.
[2025-07-26 22:43:29 +0800] [409205] [INFO] Shutting down
[2025-07-26 22:43:29 +0800] [409106] [WARNING] Maximum request limit of 2075 exceeded. Terminating process.
[2025-07-26 22:43:29 +0800] [409106] [INFO] Shutting down
[2025-07-26 22:43:29 +0800] [409205] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:29 +0800] [409205] [INFO] Application shutdown complete.
[2025-07-26 22:43:29 +0800] [409205] [INFO] Finished server process [409205]
[2025-07-26 22:43:29 +0800] [409205] [INFO] Worker exiting (pid: 409205)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:29 +0800] [410177] [INFO] Booting worker with pid: 410177
[2025-07-26 22:43:29 +0800] [410177] [INFO] Started server process [410177]
[2025-07-26 22:43:29 +0800] [410177] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:29 +0800] [409106] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:29 +0800] [409106] [INFO] Application shutdown complete.
[2025-07-26 22:43:29 +0800] [409106] [INFO] Finished server process [409106]
[2025-07-26 22:43:29 +0800] [409106] [INFO] Worker exiting (pid: 409106)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:30 +0800] [410246] [INFO] Booting worker with pid: 410246
[2025-07-26 22:43:30 +0800] [410246] [INFO] Started server process [410246]
[2025-07-26 22:43:30 +0800] [410246] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:30 +0800] [410314] [INFO] Booting worker with pid: 410314
[2025-07-26 22:43:30 +0800] [410314] [INFO] Started server process [410314]
[2025-07-26 22:43:30 +0800] [410314] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:30 +0800] [409247] [WARNING] Maximum request limit of 2038 exceeded. Terminating process.
[2025-07-26 22:43:30 +0800] [409247] [INFO] Shutting down
[2025-07-26 22:43:30 +0800] [409247] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:30 +0800] [409247] [INFO] Application shutdown complete.
[2025-07-26 22:43:30 +0800] [409247] [INFO] Finished server process [409247]
[2025-07-26 22:43:30 +0800] [409247] [INFO] Worker exiting (pid: 409247)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.60 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:30 +0800] [410177] [INFO] Application startup complete.
[2025-07-26 22:43:30 +0800] [410338] [INFO] Booting worker with pid: 410338
[2025-07-26 22:43:30 +0800] [410338] [INFO] Started server process [410338]
[2025-07-26 22:43:30 +0800] [410338] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.57 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:30 +0800] [410246] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.58 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:30 +0800] [410314] [INFO] Application startup complete.
[2025-07-26 22:43:31 +0800] [409893] [WARNING] Maximum request limit of 2045 exceeded. Terminating process.
[2025-07-26 22:43:31 +0800] [409893] [INFO] Shutting down
[2025-07-26 22:43:31 +0800] [409807] [WARNING] Maximum request limit of 2006 exceeded. Terminating process.
[2025-07-26 22:43:31 +0800] [409807] [INFO] Shutting down
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.54 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:31 +0800] [410338] [INFO] Application startup complete.
[2025-07-26 22:43:31 +0800] [409893] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:31 +0800] [409893] [INFO] Application shutdown complete.
[2025-07-26 22:43:31 +0800] [409893] [INFO] Finished server process [409893]
[2025-07-26 22:43:31 +0800] [409893] [INFO] Worker exiting (pid: 409893)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:31 +0800] [409807] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:31 +0800] [409807] [INFO] Application shutdown complete.
[2025-07-26 22:43:31 +0800] [409807] [INFO] Finished server process [409807]
[2025-07-26 22:43:31 +0800] [409807] [INFO] Worker exiting (pid: 409807)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:31 +0800] [409775] [WARNING] Maximum request limit of 2028 exceeded. Terminating process.
[2025-07-26 22:43:31 +0800] [409775] [INFO] Shutting down
[2025-07-26 22:43:31 +0800] [410719] [INFO] Booting worker with pid: 410719
[2025-07-26 22:43:31 +0800] [410719] [INFO] Started server process [410719]
[2025-07-26 22:43:31 +0800] [410719] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:31 +0800] [409836] [WARNING] Maximum request limit of 2078 exceeded. Terminating process.
[2025-07-26 22:43:31 +0800] [409836] [INFO] Shutting down
[2025-07-26 22:43:31 +0800] [409775] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:31 +0800] [409775] [INFO] Application shutdown complete.
[2025-07-26 22:43:31 +0800] [409775] [INFO] Finished server process [409775]
[2025-07-26 22:43:31 +0800] [409775] [INFO] Worker exiting (pid: 409775)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:31 +0800] [410741] [INFO] Booting worker with pid: 410741
[2025-07-26 22:43:31 +0800] [410741] [INFO] Started server process [410741]
[2025-07-26 22:43:31 +0800] [410741] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:31 +0800] [409836] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:31 +0800] [409836] [INFO] Application shutdown complete.
[2025-07-26 22:43:31 +0800] [409836] [INFO] Finished server process [409836]
[2025-07-26 22:43:31 +0800] [409836] [INFO] Worker exiting (pid: 409836)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:31 +0800] [410803] [INFO] Booting worker with pid: 410803
[2025-07-26 22:43:31 +0800] [410803] [INFO] Started server process [410803]
[2025-07-26 22:43:31 +0800] [410803] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:31 +0800] [410804] [INFO] Booting worker with pid: 410804
[2025-07-26 22:43:31 +0800] [410804] [INFO] Started server process [410804]
[2025-07-26 22:43:31 +0800] [410804] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.64 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:31 +0800] [410719] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.65 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:32 +0800] [410741] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.61 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:32 +0800] [410803] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.59 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:32 +0800] [410804] [INFO] Application startup complete.
[2025-07-26 22:43:32 +0800] [410246] [WARNING] Maximum request limit of 2092 exceeded. Terminating process.
[2025-07-26 22:43:32 +0800] [410246] [INFO] Shutting down
[2025-07-26 22:43:32 +0800] [410314] [WARNING] Maximum request limit of 2048 exceeded. Terminating process.
[2025-07-26 22:43:32 +0800] [410314] [INFO] Shutting down
[2025-07-26 22:43:32 +0800] [410246] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:32 +0800] [410246] [INFO] Application shutdown complete.
[2025-07-26 22:43:32 +0800] [410246] [INFO] Finished server process [410246]
[2025-07-26 22:43:32 +0800] [410246] [INFO] Worker exiting (pid: 410246)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:32 +0800] [410314] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:32 +0800] [410314] [INFO] Application shutdown complete.
[2025-07-26 22:43:32 +0800] [410314] [INFO] Finished server process [410314]
[2025-07-26 22:43:32 +0800] [410314] [INFO] Worker exiting (pid: 410314)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:32 +0800] [410177] [WARNING] Maximum request limit of 2091 exceeded. Terminating process.
[2025-07-26 22:43:32 +0800] [410177] [INFO] Shutting down
[2025-07-26 22:43:32 +0800] [411246] [INFO] Booting worker with pid: 411246
[2025-07-26 22:43:32 +0800] [411246] [INFO] Started server process [411246]
[2025-07-26 22:43:32 +0800] [411246] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:32 +0800] [410177] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:32 +0800] [410177] [INFO] Application shutdown complete.
[2025-07-26 22:43:32 +0800] [410177] [INFO] Finished server process [410177]
[2025-07-26 22:43:32 +0800] [410177] [INFO] Worker exiting (pid: 410177)
[2025-07-26 22:43:32 +0800] [411247] [INFO] Booting worker with pid: 411247
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:32 +0800] [411247] [INFO] Started server process [411247]
[2025-07-26 22:43:32 +0800] [411247] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:32 +0800] [410338] [WARNING] Maximum request limit of 2006 exceeded. Terminating process.
[2025-07-26 22:43:32 +0800] [410338] [INFO] Shutting down
[2025-07-26 22:43:32 +0800] [411259] [INFO] Booting worker with pid: 411259
[2025-07-26 22:43:32 +0800] [411259] [INFO] Started server process [411259]
[2025-07-26 22:43:32 +0800] [411259] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:32 +0800] [410338] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:32 +0800] [410338] [INFO] Application shutdown complete.
[2025-07-26 22:43:32 +0800] [410338] [INFO] Finished server process [410338]
[2025-07-26 22:43:32 +0800] [410338] [INFO] Worker exiting (pid: 410338)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:33 +0800] [411419] [INFO] Booting worker with pid: 411419
[2025-07-26 22:43:33 +0800] [411419] [INFO] Started server process [411419]
[2025-07-26 22:43:33 +0800] [411419] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.63 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:33 +0800] [411246] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.62 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:33 +0800] [411247] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.59 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:33 +0800] [411259] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.55 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:33 +0800] [411419] [INFO] Application startup complete.
[2025-07-26 22:43:33 +0800] [410803] [WARNING] Maximum request limit of 2020 exceeded. Terminating process.
[2025-07-26 22:43:33 +0800] [410803] [INFO] Shutting down
[2025-07-26 22:43:33 +0800] [410803] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:33 +0800] [410803] [INFO] Application shutdown complete.
[2025-07-26 22:43:33 +0800] [410803] [INFO] Finished server process [410803]
[2025-07-26 22:43:33 +0800] [410803] [INFO] Worker exiting (pid: 410803)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:33 +0800] [410741] [WARNING] Maximum request limit of 2051 exceeded. Terminating process.
[2025-07-26 22:43:33 +0800] [410741] [INFO] Shutting down
[2025-07-26 22:43:33 +0800] [411732] [INFO] Booting worker with pid: 411732
[2025-07-26 22:43:33 +0800] [411732] [INFO] Started server process [411732]
[2025-07-26 22:43:33 +0800] [411732] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:33 +0800] [410741] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:33 +0800] [410741] [INFO] Application shutdown complete.
[2025-07-26 22:43:33 +0800] [410741] [INFO] Finished server process [410741]
[2025-07-26 22:43:33 +0800] [410741] [INFO] Worker exiting (pid: 410741)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:34 +0800] [410804] [WARNING] Maximum request limit of 2016 exceeded. Terminating process.
[2025-07-26 22:43:34 +0800] [410804] [INFO] Shutting down
[2025-07-26 22:43:34 +0800] [411790] [INFO] Booting worker with pid: 411790
[2025-07-26 22:43:34 +0800] [411790] [INFO] Started server process [411790]
[2025-07-26 22:43:34 +0800] [411790] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:34 +0800] [410804] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:34 +0800] [410804] [INFO] Application shutdown complete.
[2025-07-26 22:43:34 +0800] [410804] [INFO] Finished server process [410804]
[2025-07-26 22:43:34 +0800] [410804] [INFO] Worker exiting (pid: 410804)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:34 +0800] [410719] [WARNING] Maximum request limit of 2040 exceeded. Terminating process.
[2025-07-26 22:43:34 +0800] [410719] [INFO] Shutting down
[2025-07-26 22:43:34 +0800] [411803] [INFO] Booting worker with pid: 411803
[2025-07-26 22:43:34 +0800] [411803] [INFO] Started server process [411803]
[2025-07-26 22:43:34 +0800] [411803] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:34 +0800] [410719] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:34 +0800] [410719] [INFO] Application shutdown complete.
[2025-07-26 22:43:34 +0800] [410719] [INFO] Finished server process [410719]
[2025-07-26 22:43:34 +0800] [410719] [INFO] Worker exiting (pid: 410719)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.59 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:34 +0800] [411732] [INFO] Application startup complete.
[2025-07-26 22:43:34 +0800] [411807] [INFO] Booting worker with pid: 411807
[2025-07-26 22:43:34 +0800] [411807] [INFO] Started server process [411807]
[2025-07-26 22:43:34 +0800] [411807] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.58 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:34 +0800] [411790] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.57 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:34 +0800] [411803] [INFO] Application startup complete.
[2025-07-26 22:43:35 +0800] [411419] [WARNING] Maximum request limit of 2031 exceeded. Terminating process.
[2025-07-26 22:43:35 +0800] [411419] [INFO] Shutting down
[2025-07-26 22:43:35 +0800] [411259] [WARNING] Maximum request limit of 2073 exceeded. Terminating process.
[2025-07-26 22:43:35 +0800] [411259] [INFO] Shutting down
[2025-07-26 22:43:35 +0800] [411246] [WARNING] Maximum request limit of 2062 exceeded. Terminating process.
[2025-07-26 22:43:35 +0800] [411246] [INFO] Shutting down
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.54 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:35 +0800] [411807] [INFO] Application startup complete.
[2025-07-26 22:43:35 +0800] [411419] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:35 +0800] [411419] [INFO] Application shutdown complete.
[2025-07-26 22:43:35 +0800] [411419] [INFO] Finished server process [411419]
[2025-07-26 22:43:35 +0800] [411419] [INFO] Worker exiting (pid: 411419)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:35 +0800] [411259] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:35 +0800] [411259] [INFO] Application shutdown complete.
[2025-07-26 22:43:35 +0800] [411259] [INFO] Finished server process [411259]
[2025-07-26 22:43:35 +0800] [411259] [INFO] Worker exiting (pid: 411259)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:35 +0800] [411246] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:35 +0800] [411246] [INFO] Application shutdown complete.
[2025-07-26 22:43:35 +0800] [411246] [INFO] Finished server process [411246]
[2025-07-26 22:43:35 +0800] [411246] [INFO] Worker exiting (pid: 411246)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:35 +0800] [411247] [WARNING] Maximum request limit of 2040 exceeded. Terminating process.
[2025-07-26 22:43:35 +0800] [411247] [INFO] Shutting down
[2025-07-26 22:43:35 +0800] [412060] [INFO] Booting worker with pid: 412060
[2025-07-26 22:43:35 +0800] [412060] [INFO] Started server process [412060]
[2025-07-26 22:43:35 +0800] [412060] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:35 +0800] [411247] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:35 +0800] [411247] [INFO] Application shutdown complete.
[2025-07-26 22:43:35 +0800] [411247] [INFO] Finished server process [411247]
[2025-07-26 22:43:35 +0800] [411247] [INFO] Worker exiting (pid: 411247)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:35 +0800] [412061] [INFO] Booting worker with pid: 412061
[2025-07-26 22:43:35 +0800] [412061] [INFO] Started server process [412061]
[2025-07-26 22:43:35 +0800] [412061] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:35 +0800] [412077] [INFO] Booting worker with pid: 412077
[2025-07-26 22:43:35 +0800] [412077] [INFO] Started server process [412077]
[2025-07-26 22:43:35 +0800] [412077] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:35 +0800] [412098] [INFO] Booting worker with pid: 412098
[2025-07-26 22:43:35 +0800] [412098] [INFO] Started server process [412098]
[2025-07-26 22:43:35 +0800] [412098] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.67 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:36 +0800] [412060] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.64 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:36 +0800] [412061] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.65 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:36 +0800] [412077] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.60 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:36 +0800] [412098] [INFO] Application startup complete.
[2025-07-26 22:43:36 +0800] [411803] [WARNING] Maximum request limit of 2007 exceeded. Terminating process.
[2025-07-26 22:43:36 +0800] [411803] [INFO] Shutting down
[2025-07-26 22:43:36 +0800] [411790] [WARNING] Maximum request limit of 2060 exceeded. Terminating process.
[2025-07-26 22:43:36 +0800] [411790] [INFO] Shutting down
[2025-07-26 22:43:36 +0800] [411732] [WARNING] Maximum request limit of 2034 exceeded. Terminating process.
[2025-07-26 22:43:36 +0800] [411732] [INFO] Shutting down
[2025-07-26 22:43:36 +0800] [411803] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:36 +0800] [411803] [INFO] Application shutdown complete.
[2025-07-26 22:43:36 +0800] [411803] [INFO] Finished server process [411803]
[2025-07-26 22:43:36 +0800] [411803] [INFO] Worker exiting (pid: 411803)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:36 +0800] [411790] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:36 +0800] [411790] [INFO] Application shutdown complete.
[2025-07-26 22:43:36 +0800] [411790] [INFO] Finished server process [411790]
[2025-07-26 22:43:36 +0800] [411790] [INFO] Worker exiting (pid: 411790)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:36 +0800] [411732] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:36 +0800] [411732] [INFO] Application shutdown complete.
[2025-07-26 22:43:36 +0800] [411732] [INFO] Finished server process [411732]
[2025-07-26 22:43:36 +0800] [411732] [INFO] Worker exiting (pid: 411732)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:36 +0800] [412672] [INFO] Booting worker with pid: 412672
[2025-07-26 22:43:36 +0800] [412672] [INFO] Started server process [412672]
[2025-07-26 22:43:36 +0800] [412672] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:36 +0800] [412692] [INFO] Booting worker with pid: 412692
[2025-07-26 22:43:36 +0800] [412692] [INFO] Started server process [412692]
[2025-07-26 22:43:36 +0800] [412692] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:36 +0800] [412726] [INFO] Booting worker with pid: 412726
[2025-07-26 22:43:36 +0800] [412726] [INFO] Started server process [412726]
[2025-07-26 22:43:36 +0800] [412726] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.61 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:37 +0800] [412672] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.58 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:37 +0800] [412692] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.58 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:37 +0800] [412726] [INFO] Application startup complete.
[2025-07-26 22:43:37 +0800] [411807] [WARNING] Maximum request limit of 2002 exceeded. Terminating process.
[2025-07-26 22:43:37 +0800] [411807] [INFO] Shutting down
[2025-07-26 22:43:37 +0800] [411807] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:37 +0800] [411807] [INFO] Application shutdown complete.
[2025-07-26 22:43:37 +0800] [411807] [INFO] Finished server process [411807]
[2025-07-26 22:43:37 +0800] [411807] [INFO] Worker exiting (pid: 411807)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:37 +0800] [413309] [INFO] Booting worker with pid: 413309
[2025-07-26 22:43:37 +0800] [413309] [INFO] Started server process [413309]
[2025-07-26 22:43:37 +0800] [413309] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:37 +0800] [412060] [WARNING] Maximum request limit of 2002 exceeded. Terminating process.
[2025-07-26 22:43:37 +0800] [412060] [INFO] Shutting down
[2025-07-26 22:43:37 +0800] [412098] [WARNING] Maximum request limit of 2012 exceeded. Terminating process.
[2025-07-26 22:43:37 +0800] [412098] [INFO] Shutting down
[2025-07-26 22:43:37 +0800] [412061] [WARNING] Maximum request limit of 2053 exceeded. Terminating process.
[2025-07-26 22:43:37 +0800] [412061] [INFO] Shutting down
[2025-07-26 22:43:37 +0800] [412060] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:37 +0800] [412060] [INFO] Application shutdown complete.
[2025-07-26 22:43:37 +0800] [412060] [INFO] Finished server process [412060]
[2025-07-26 22:43:37 +0800] [412060] [INFO] Worker exiting (pid: 412060)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:37 +0800] [412098] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:37 +0800] [412098] [INFO] Application shutdown complete.
[2025-07-26 22:43:37 +0800] [412098] [INFO] Finished server process [412098]
[2025-07-26 22:43:37 +0800] [412098] [INFO] Worker exiting (pid: 412098)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:37 +0800] [412061] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:37 +0800] [412061] [INFO] Application shutdown complete.
[2025-07-26 22:43:37 +0800] [412061] [INFO] Finished server process [412061]
[2025-07-26 22:43:37 +0800] [412061] [INFO] Worker exiting (pid: 412061)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:37 +0800] [413353] [INFO] Booting worker with pid: 413353
[2025-07-26 22:43:38 +0800] [413353] [INFO] Started server process [413353]
[2025-07-26 22:43:38 +0800] [413353] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:38 +0800] [413571] [INFO] Booting worker with pid: 413571
[2025-07-26 22:43:38 +0800] [413571] [INFO] Started server process [413571]
[2025-07-26 22:43:38 +0800] [413571] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:38 +0800] [413602] [INFO] Booting worker with pid: 413602
[2025-07-26 22:43:38 +0800] [413602] [INFO] Started server process [413602]
[2025-07-26 22:43:38 +0800] [413602] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:38 +0800] [412077] [WARNING] Maximum request limit of 2094 exceeded. Terminating process.
[2025-07-26 22:43:38 +0800] [412077] [INFO] Shutting down
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.58 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:38 +0800] [413309] [INFO] Application startup complete.
[2025-07-26 22:43:38 +0800] [412077] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:38 +0800] [412077] [INFO] Application shutdown complete.
[2025-07-26 22:43:38 +0800] [412077] [INFO] Finished server process [412077]
[2025-07-26 22:43:38 +0800] [412077] [INFO] Worker exiting (pid: 412077)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:38 +0800] [413669] [INFO] Booting worker with pid: 413669
[2025-07-26 22:43:38 +0800] [413669] [INFO] Started server process [413669]
[2025-07-26 22:43:38 +0800] [413669] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.61 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:38 +0800] [413353] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.61 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:38 +0800] [413571] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.59 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:38 +0800] [413602] [INFO] Application startup complete.
[2025-07-26 22:43:38 +0800] [412672] [WARNING] Maximum request limit of 2061 exceeded. Terminating process.
[2025-07-26 22:43:38 +0800] [412672] [INFO] Shutting down
[2025-07-26 22:43:39 +0800] [412726] [WARNING] Maximum request limit of 2090 exceeded. Terminating process.
[2025-07-26 22:43:39 +0800] [412726] [INFO] Shutting down
[2025-07-26 22:43:39 +0800] [412692] [WARNING] Maximum request limit of 2002 exceeded. Terminating process.
[2025-07-26 22:43:39 +0800] [412692] [INFO] Shutting down
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.56 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:39 +0800] [413669] [INFO] Application startup complete.
[2025-07-26 22:43:39 +0800] [412672] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:39 +0800] [412672] [INFO] Application shutdown complete.
[2025-07-26 22:43:39 +0800] [412672] [INFO] Finished server process [412672]
[2025-07-26 22:43:39 +0800] [412672] [INFO] Worker exiting (pid: 412672)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:39 +0800] [412726] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:39 +0800] [412726] [INFO] Application shutdown complete.
[2025-07-26 22:43:39 +0800] [412726] [INFO] Finished server process [412726]
[2025-07-26 22:43:39 +0800] [412726] [INFO] Worker exiting (pid: 412726)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:39 +0800] [412692] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:39 +0800] [412692] [INFO] Application shutdown complete.
[2025-07-26 22:43:39 +0800] [412692] [INFO] Finished server process [412692]
[2025-07-26 22:43:39 +0800] [412692] [INFO] Worker exiting (pid: 412692)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:39 +0800] [414144] [INFO] Booting worker with pid: 414144
[2025-07-26 22:43:39 +0800] [414144] [INFO] Started server process [414144]
[2025-07-26 22:43:39 +0800] [414144] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:39 +0800] [414167] [INFO] Booting worker with pid: 414167
[2025-07-26 22:43:39 +0800] [414167] [INFO] Started server process [414167]
[2025-07-26 22:43:39 +0800] [414167] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:39 +0800] [414269] [INFO] Booting worker with pid: 414269
[2025-07-26 22:43:39 +0800] [414269] [INFO] Started server process [414269]
[2025-07-26 22:43:39 +0800] [414269] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.59 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:39 +0800] [414144] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.57 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:39 +0800] [414167] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.56 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:39 +0800] [414269] [INFO] Application startup complete.
[2025-07-26 22:43:40 +0800] [413309] [WARNING] Maximum request limit of 2088 exceeded. Terminating process.
[2025-07-26 22:43:40 +0800] [413309] [INFO] Shutting down
[2025-07-26 22:43:40 +0800] [413571] [WARNING] Maximum request limit of 2083 exceeded. Terminating process.
[2025-07-26 22:43:40 +0800] [413571] [INFO] Shutting down
[2025-07-26 22:43:40 +0800] [413353] [WARNING] Maximum request limit of 2017 exceeded. Terminating process.
[2025-07-26 22:43:40 +0800] [413353] [INFO] Shutting down
[2025-07-26 22:43:40 +0800] [413602] [WARNING] Maximum request limit of 2054 exceeded. Terminating process.
[2025-07-26 22:43:40 +0800] [413602] [INFO] Shutting down
[2025-07-26 22:43:40 +0800] [413309] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:40 +0800] [413309] [INFO] Application shutdown complete.
[2025-07-26 22:43:40 +0800] [413309] [INFO] Finished server process [413309]
[2025-07-26 22:43:40 +0800] [413309] [INFO] Worker exiting (pid: 413309)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:40 +0800] [413571] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:40 +0800] [413571] [INFO] Application shutdown complete.
[2025-07-26 22:43:40 +0800] [413571] [INFO] Finished server process [413571]
[2025-07-26 22:43:40 +0800] [413571] [INFO] Worker exiting (pid: 413571)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:40 +0800] [413353] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:40 +0800] [413353] [INFO] Application shutdown complete.
[2025-07-26 22:43:40 +0800] [413353] [INFO] Finished server process [413353]
[2025-07-26 22:43:40 +0800] [413602] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:40 +0800] [413602] [INFO] Application shutdown complete.
[2025-07-26 22:43:40 +0800] [413602] [INFO] Finished server process [413602]
[2025-07-26 22:43:40 +0800] [413353] [INFO] Worker exiting (pid: 413353)
[2025-07-26 22:43:40 +0800] [413602] [INFO] Worker exiting (pid: 413602)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:40 +0800] [414589] [INFO] Booting worker with pid: 414589
[2025-07-26 22:43:40 +0800] [414589] [INFO] Started server process [414589]
[2025-07-26 22:43:40 +0800] [414589] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:40 +0800] [414612] [INFO] Booting worker with pid: 414612
[2025-07-26 22:43:40 +0800] [414612] [INFO] Started server process [414612]
[2025-07-26 22:43:40 +0800] [414612] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:40 +0800] [414615] [INFO] Booting worker with pid: 414615
[2025-07-26 22:43:40 +0800] [414615] [INFO] Started server process [414615]
[2025-07-26 22:43:40 +0800] [414615] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:40 +0800] [414719] [INFO] Booting worker with pid: 414719
[2025-07-26 22:43:40 +0800] [414719] [INFO] Started server process [414719]
[2025-07-26 22:43:40 +0800] [414719] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.62 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:41 +0800] [414589] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.62 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:41 +0800] [414612] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.63 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:41 +0800] [414615] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.61 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:41 +0800] [414719] [INFO] Application startup complete.
[2025-07-26 22:43:41 +0800] [413669] [WARNING] Maximum request limit of 2009 exceeded. Terminating process.
[2025-07-26 22:43:41 +0800] [413669] [INFO] Shutting down
[2025-07-26 22:43:41 +0800] [414144] [WARNING] Maximum request limit of 2052 exceeded. Terminating process.
[2025-07-26 22:43:41 +0800] [414144] [INFO] Shutting down
[2025-07-26 22:43:41 +0800] [413669] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:41 +0800] [413669] [INFO] Application shutdown complete.
[2025-07-26 22:43:41 +0800] [413669] [INFO] Finished server process [413669]
[2025-07-26 22:43:41 +0800] [413669] [INFO] Worker exiting (pid: 413669)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:41 +0800] [414269] [WARNING] Maximum request limit of 2084 exceeded. Terminating process.
[2025-07-26 22:43:41 +0800] [414269] [INFO] Shutting down
[2025-07-26 22:43:41 +0800] [414144] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:41 +0800] [414144] [INFO] Application shutdown complete.
[2025-07-26 22:43:41 +0800] [414144] [INFO] Finished server process [414144]
[2025-07-26 22:43:41 +0800] [414144] [INFO] Worker exiting (pid: 414144)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:41 +0800] [414269] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:41 +0800] [414269] [INFO] Application shutdown complete.
[2025-07-26 22:43:41 +0800] [414269] [INFO] Finished server process [414269]
[2025-07-26 22:43:41 +0800] [414269] [INFO] Worker exiting (pid: 414269)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:41 +0800] [415266] [INFO] Booting worker with pid: 415266
[2025-07-26 22:43:41 +0800] [415266] [INFO] Started server process [415266]
[2025-07-26 22:43:41 +0800] [415266] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:41 +0800] [415281] [INFO] Booting worker with pid: 415281
[2025-07-26 22:43:41 +0800] [415281] [INFO] Started server process [415281]
[2025-07-26 22:43:41 +0800] [415281] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:41 +0800] [415282] [INFO] Booting worker with pid: 415282
[2025-07-26 22:43:41 +0800] [415282] [INFO] Started server process [415282]
[2025-07-26 22:43:41 +0800] [415282] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:42 +0800] [414167] [WARNING] Maximum request limit of 2059 exceeded. Terminating process.
[2025-07-26 22:43:42 +0800] [414167] [INFO] Shutting down
[2025-07-26 22:43:42 +0800] [414167] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:42 +0800] [414167] [INFO] Application shutdown complete.
[2025-07-26 22:43:42 +0800] [414167] [INFO] Finished server process [414167]
[2025-07-26 22:43:42 +0800] [414167] [INFO] Worker exiting (pid: 414167)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:42 +0800] [415408] [INFO] Booting worker with pid: 415408
[2025-07-26 22:43:42 +0800] [415408] [INFO] Started server process [415408]
[2025-07-26 22:43:42 +0800] [415408] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.60 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:42 +0800] [415266] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.60 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:42 +0800] [415281] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.60 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:42 +0800] [415282] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.54 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:42 +0800] [415408] [INFO] Application startup complete.
[2025-07-26 22:43:42 +0800] [414615] [WARNING] Maximum request limit of 2016 exceeded. Terminating process.
[2025-07-26 22:43:42 +0800] [414615] [INFO] Shutting down
[2025-07-26 22:43:42 +0800] [414589] [WARNING] Maximum request limit of 2073 exceeded. Terminating process.
[2025-07-26 22:43:42 +0800] [414589] [INFO] Shutting down
[2025-07-26 22:43:43 +0800] [414615] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:43 +0800] [414615] [INFO] Application shutdown complete.
[2025-07-26 22:43:43 +0800] [414615] [INFO] Finished server process [414615]
[2025-07-26 22:43:43 +0800] [414615] [INFO] Worker exiting (pid: 414615)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:43 +0800] [414589] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:43 +0800] [414589] [INFO] Application shutdown complete.
[2025-07-26 22:43:43 +0800] [414589] [INFO] Finished server process [414589]
[2025-07-26 22:43:43 +0800] [414589] [INFO] Worker exiting (pid: 414589)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:43 +0800] [414719] [WARNING] Maximum request limit of 2032 exceeded. Terminating process.
[2025-07-26 22:43:43 +0800] [414719] [INFO] Shutting down
[2025-07-26 22:43:43 +0800] [414612] [WARNING] Maximum request limit of 2061 exceeded. Terminating process.
[2025-07-26 22:43:43 +0800] [414612] [INFO] Shutting down
[2025-07-26 22:43:43 +0800] [415846] [INFO] Booting worker with pid: 415846
[2025-07-26 22:43:43 +0800] [414719] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:43 +0800] [414719] [INFO] Application shutdown complete.
[2025-07-26 22:43:43 +0800] [414719] [INFO] Finished server process [414719]
[2025-07-26 22:43:43 +0800] [414719] [INFO] Worker exiting (pid: 414719)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:43 +0800] [415846] [INFO] Started server process [415846]
[2025-07-26 22:43:43 +0800] [415846] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:43 +0800] [414612] [INFO] Waiting for application shutdown.
[2025-07-26 22:43:43 +0800] [414612] [INFO] Application shutdown complete.
[2025-07-26 22:43:43 +0800] [414612] [INFO] Finished server process [414612]
[2025-07-26 22:43:43 +0800] [414612] [INFO] Worker exiting (pid: 414612)
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

[2025-07-26 22:43:43 +0800] [415913] [INFO] Booting worker with pid: 415913
[2025-07-26 22:43:43 +0800] [415913] [INFO] Started server process [415913]
[2025-07-26 22:43:43 +0800] [415913] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:43 +0800] [415921] [INFO] Booting worker with pid: 415921
[2025-07-26 22:43:43 +0800] [415921] [INFO] Started server process [415921]
[2025-07-26 22:43:43 +0800] [415921] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:43:43 +0800] [415923] [INFO] Booting worker with pid: 415923
[2025-07-26 22:43:43 +0800] [415923] [INFO] Started server process [415923]
[2025-07-26 22:43:43 +0800] [415923] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.64 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:43 +0800] [415846] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.64 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:43 +0800] [415913] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.62 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:44 +0800] [415921] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 0.60 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:43:44 +0800] [415923] [INFO] Application startup complete.
[2025-07-26 22:45:12 +0800] [329488] [INFO] Handling signal: term
[2025-07-26 22:45:12 +0800] [415282] [INFO] Shutting down
[2025-07-26 22:45:12 +0800] [415921] [INFO] Shutting down
[2025-07-26 22:45:12 +0800] [415913] [INFO] Shutting down
[2025-07-26 22:45:12 +0800] [415281] [INFO] Shutting down
[2025-07-26 22:45:12 +0800] [415846] [INFO] Shutting down
[2025-07-26 22:45:12 +0800] [415266] [INFO] Shutting down
[2025-07-26 22:45:12 +0800] [415408] [INFO] Shutting down
[2025-07-26 22:45:12 +0800] [415923] [INFO] Shutting down
[2025-07-26 22:45:12 +0800] [415282] [INFO] Waiting for application shutdown.
[2025-07-26 22:45:12 +0800] [415282] [INFO] Application shutdown complete.
[2025-07-26 22:45:12 +0800] [415282] [INFO] Finished server process [415282]
[2025-07-26 22:45:12 +0800] [329488] [ERROR] Worker (pid:415282) was sent SIGTERM!
[2025-07-26 22:45:12 +0800] [415921] [INFO] Waiting for application shutdown.
[2025-07-26 22:45:12 +0800] [415921] [INFO] Application shutdown complete.
[2025-07-26 22:45:12 +0800] [415921] [INFO] Finished server process [415921]
[2025-07-26 22:45:12 +0800] [415913] [INFO] Waiting for application shutdown.
[2025-07-26 22:45:12 +0800] [415913] [INFO] Application shutdown complete.
[2025-07-26 22:45:12 +0800] [415913] [INFO] Finished server process [415913]
[2025-07-26 22:45:12 +0800] [329488] [ERROR] Worker (pid:415921) was sent SIGTERM!
[2025-07-26 22:45:12 +0800] [415281] [INFO] Waiting for application shutdown.
[2025-07-26 22:45:12 +0800] [415281] [INFO] Application shutdown complete.
[2025-07-26 22:45:12 +0800] [415281] [INFO] Finished server process [415281]
[2025-07-26 22:45:12 +0800] [329488] [ERROR] Worker (pid:415913) was sent SIGTERM!
[2025-07-26 22:45:12 +0800] [415846] [INFO] Waiting for application shutdown.
[2025-07-26 22:45:12 +0800] [415846] [INFO] Application shutdown complete.
[2025-07-26 22:45:12 +0800] [415846] [INFO] Finished server process [415846]
[2025-07-26 22:45:12 +0800] [329488] [ERROR] Worker (pid:415281) was sent SIGTERM!
[2025-07-26 22:45:12 +0800] [329488] [ERROR] Worker (pid:415846) was sent SIGTERM!
[2025-07-26 22:45:12 +0800] [415266] [INFO] Waiting for application shutdown.
[2025-07-26 22:45:12 +0800] [415266] [INFO] Application shutdown complete.
[2025-07-26 22:45:12 +0800] [415266] [INFO] Finished server process [415266]
[2025-07-26 22:45:12 +0800] [329488] [ERROR] Worker (pid:415266) was sent SIGTERM!
[2025-07-26 22:45:12 +0800] [415408] [INFO] Waiting for application shutdown.
[2025-07-26 22:45:12 +0800] [415408] [INFO] Application shutdown complete.
[2025-07-26 22:45:12 +0800] [415408] [INFO] Finished server process [415408]
[2025-07-26 22:45:12 +0800] [329488] [ERROR] Worker (pid:415408) was sent SIGTERM!
[2025-07-26 22:45:12 +0800] [415923] [INFO] Waiting for application shutdown.
[2025-07-26 22:45:12 +0800] [415923] [INFO] Application shutdown complete.
[2025-07-26 22:45:12 +0800] [415923] [INFO] Finished server process [415923]
[2025-07-26 22:45:12 +0800] [329488] [ERROR] Worker (pid:415923) was sent SIGTERM!
[2025-07-26 22:45:12 +0800] [329488] [INFO] Shutting down: Master
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

