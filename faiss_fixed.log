nohup: ignoring input
INFO:__main__:🧵 使用同步模型 (无线程池) - 避免内存不足问题
INFO:resource_manager:Applying resource limits: 16 CPU cores, 64.0GB RAM
INFO:resource_manager:CPU affinity set to cores: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]
INFO:resource_manager:Memory limit set to 64.0GB
INFO:__main__:✅ 资源限制已应用: 16核心, 64GB内存
INFO:__main__:🧵 FAISS OpenMP线程数设置为: 2
Looking in indexes: http://mirrors.aliyun.com/pypi/simple/
Collecting gunicorn
  Downloading http://mirrors.aliyun.com/pypi/packages/cb/7d/6dac2a6e1eba33ee43f318edbed4ff29151a49b5d37f080aad1e6469bca4/gunicorn-23.0.0-py3-none-any.whl (85 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 85.0/85.0 kB 1.4 MB/s eta 0:00:00
Requirement already satisfied: packaging in /usr/local/lib/python3.11/site-packages (from gunicorn) (25.0)
Installing collected packages: gunicorn
Successfully installed gunicorn-23.0.0
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
❌ Gunicorn未安装，正在安装...
✅ Gunicorn安装完成，请重新运行
