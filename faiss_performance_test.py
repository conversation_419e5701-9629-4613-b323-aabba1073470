#!/usr/bin/env python3
"""
直接测试 FAISS 服务器性能的脚本
跳过 VectorDBBench 的数据集下载，直接使用服务器端已有的数据
"""

import time
import random
import requests
import numpy as np
import concurrent.futures
from typing import List
import statistics

class FaissDirectTester:
    def __init__(self, server_url: str):
        self.server_url = server_url
        self.session = requests.Session()
        
    def get_server_status(self):
        """获取服务器状态"""
        try:
            resp = self.session.get(f"{self.server_url}/status")
            resp.raise_for_status()
            return resp.json()
        except Exception as e:
            print(f"❌ 获取服务器状态失败: {e}")
            return None
    
    def generate_random_query(self, dim: int) -> List[float]:
        """生成随机查询向量"""
        # 生成随机向量并归一化（用于余弦相似度）
        vector = np.random.randn(dim).astype(np.float32)
        vector = vector / np.linalg.norm(vector)
        return vector.tolist()
    
    def search_single(self, query: List[float], k: int = 100) -> tuple:
        """执行单次搜索，返回 (延迟, 是否成功)"""
        start_time = time.perf_counter()
        try:
            resp = self.session.post(
                f"{self.server_url}/search",
                json={"query": query, "topk": k},
                timeout=10
            )
            resp.raise_for_status()
            latency = time.perf_counter() - start_time
            return latency, True
        except Exception as e:
            latency = time.perf_counter() - start_time
            return latency, False
    
    def run_serial_test(self, dim: int, duration: int = 30, k: int = 100):
        """运行串行搜索测试"""
        print(f"\n🔍 开始串行搜索测试 (持续 {duration} 秒)...")
        
        start_time = time.perf_counter()
        count = 0
        failed_count = 0
        latencies = []
        
        while time.perf_counter() - start_time < duration:
            query = self.generate_random_query(dim)
            latency, success = self.search_single(query, k)
            
            if success:
                count += 1
                latencies.append(latency)
            else:
                failed_count += 1
            
            if count % 100 == 0 and count > 0:
                current_qps = count / (time.perf_counter() - start_time)
                print(f"   已完成 {count} 次搜索, 当前 QPS: {current_qps:.2f}")
        
        total_time = time.perf_counter() - start_time
        
        if latencies:
            avg_latency = statistics.mean(latencies)
            p99_latency = np.percentile(latencies, 99)
            qps = count / total_time
            
            print(f"\n📊 串行测试结果:")
            print(f"   总搜索次数: {count}")
            print(f"   失败次数: {failed_count}")
            print(f"   总耗时: {total_time:.2f} 秒")
            print(f"   QPS: {qps:.2f}")
            print(f"   平均延迟: {avg_latency*1000:.2f} ms")
            print(f"   P99延迟: {p99_latency*1000:.2f} ms")
        else:
            print("❌ 没有成功的搜索请求")
    
    def worker_thread(self, dim: int, duration: int, k: int, worker_id: int):
        """并发测试的工作线程"""
        start_time = time.perf_counter()
        count = 0
        failed_count = 0
        latencies = []
        
        while time.perf_counter() - start_time < duration:
            query = self.generate_random_query(dim)
            latency, success = self.search_single(query, k)
            
            if success:
                count += 1
                latencies.append(latency)
            else:
                failed_count += 1
        
        return count, failed_count, latencies
    
    def run_concurrent_test(self, dim: int, concurrency: int, duration: int = 30, k: int = 100):
        """运行并发搜索测试"""
        print(f"\n🚀 开始并发搜索测试 (并发数: {concurrency}, 持续 {duration} 秒)...")
        
        start_time = time.perf_counter()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=concurrency) as executor:
            futures = [
                executor.submit(self.worker_thread, dim, duration, k, i)
                for i in range(concurrency)
            ]
            
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        total_time = time.perf_counter() - start_time
        
        # 汇总结果
        total_count = sum(r[0] for r in results)
        total_failed = sum(r[1] for r in results)
        all_latencies = []
        for r in results:
            all_latencies.extend(r[2])
        
        if all_latencies:
            avg_latency = statistics.mean(all_latencies)
            p99_latency = np.percentile(all_latencies, 99)
            qps = total_count / total_time
            
            print(f"\n📊 并发测试结果 (并发数: {concurrency}):")
            print(f"   总搜索次数: {total_count}")
            print(f"   失败次数: {total_failed}")
            print(f"   总耗时: {total_time:.2f} 秒")
            print(f"   QPS: {qps:.2f}")
            print(f"   平均延迟: {avg_latency*1000:.2f} ms")
            print(f"   P99延迟: {p99_latency*1000:.2f} ms")
            
            return {
                'concurrency': concurrency,
                'total_count': total_count,
                'failed_count': total_failed,
                'qps': qps,
                'avg_latency_ms': avg_latency * 1000,
                'p99_latency_ms': p99_latency * 1000
            }
        else:
            print("❌ 没有成功的搜索请求")
            return None

def main():
    server_url = "http://10.1.180.72:8005"
    tester = FaissDirectTester(server_url)
    
    print("🌐 FAISS 服务器性能测试")
    print("=" * 50)
    
    # 检查服务器状态
    status = tester.get_server_status()
    if not status:
        print("❌ 无法连接到服务器")
        return
    
    print(f"📊 服务器状态:")
    vector_count = status.get('vectors_count', status.get('total_vectors', status.get('vector_count', 0)))
    print(f"   向量数量: {vector_count:,}")
    print(f"   索引类型: {status.get('index_type', 'Unknown')}")
    print(f"   维度: {status.get('dimension', 0)}")

    dim = status.get('dimension', 768)
    
    if vector_count == 0:
        print("❌ 服务器中没有向量数据")
        return
    
    # 运行串行测试
    tester.run_serial_test(dim, duration=30)
    
    # 运行并发测试
    concurrency_levels = [8, 16, 32, 64, 128]
    results = []
    
    for concurrency in concurrency_levels:
        result = tester.run_concurrent_test(dim, concurrency, duration=30)
        if result:
            results.append(result)
    
    # 输出汇总结果
    print(f"\n📈 并发测试汇总:")
    print(f"{'并发数':<8} {'QPS':<10} {'平均延迟(ms)':<12} {'P99延迟(ms)':<12} {'失败次数':<8}")
    print("-" * 60)
    for result in results:
        print(f"{result['concurrency']:<8} {result['qps']:<10.2f} {result['avg_latency_ms']:<12.2f} {result['p99_latency_ms']:<12.2f} {result['failed_count']:<8}")

if __name__ == "__main__":
    main()
