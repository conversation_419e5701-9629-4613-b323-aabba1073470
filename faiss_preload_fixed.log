nohup: ignoring input
/home/<USER>/VectorDBBench/smart_faiss_server.py:36: DeprecationWarning: 
        on_event is deprecated, use lifespan event handlers instead.

        Read more about it in the
        [FastAPI docs for Lifespan Events](https://fastapi.tiangolo.com/advanced/events/).
        
  @app.on_event("startup")
INFO:__main__:🧵 使用同步模型 (无线程池) - 避免内存不足问题
INFO:resource_manager:Applying resource limits: 16 CPU cores, 64.0GB RAM
INFO:resource_manager:CPU affinity set to cores: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]
INFO:resource_manager:Memory limit set to 64.0GB
INFO:__main__:✅ 资源限制已应用: 16核心, 64GB内存
INFO:__main__:🧵 FAISS OpenMP线程数设置为: 2
[2025-07-26 22:36:16 +0800] [217983] [INFO] Starting gunicorn 23.0.0
[2025-07-26 22:36:16 +0800] [217983] [INFO] Listening at: http://0.0.0.0:8005 (217983)
[2025-07-26 22:36:16 +0800] [217983] [INFO] Using worker: uvicorn.workers.UvicornWorker
[2025-07-26 22:36:16 +0800] [218046] [INFO] Booting worker with pid: 218046
[2025-07-26 22:36:16 +0800] [218046] [INFO] Started server process [218046]
[2025-07-26 22:36:16 +0800] [218046] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:36:16 +0800] [218047] [INFO] Booting worker with pid: 218047
[2025-07-26 22:36:16 +0800] [218047] [INFO] Started server process [218047]
[2025-07-26 22:36:16 +0800] [218047] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:36:16 +0800] [218048] [INFO] Booting worker with pid: 218048
[2025-07-26 22:36:16 +0800] [218048] [INFO] Started server process [218048]
[2025-07-26 22:36:16 +0800] [218048] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:36:17 +0800] [218116] [INFO] Booting worker with pid: 218116
[2025-07-26 22:36:17 +0800] [218116] [INFO] Started server process [218116]
[2025-07-26 22:36:17 +0800] [218116] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:36:17 +0800] [218260] [INFO] Booting worker with pid: 218260
[2025-07-26 22:36:17 +0800] [218260] [INFO] Started server process [218260]
[2025-07-26 22:36:17 +0800] [218260] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:36:17 +0800] [218326] [INFO] Booting worker with pid: 218326
[2025-07-26 22:36:17 +0800] [218326] [INFO] Started server process [218326]
[2025-07-26 22:36:17 +0800] [218326] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:36:17 +0800] [218435] [INFO] Booting worker with pid: 218435
[2025-07-26 22:36:17 +0800] [218437] [INFO] Booting worker with pid: 218437
[2025-07-26 22:36:17 +0800] [218435] [INFO] Started server process [218435]
[2025-07-26 22:36:17 +0800] [218435] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
[2025-07-26 22:36:17 +0800] [218437] [INFO] Started server process [218437]
[2025-07-26 22:36:17 +0800] [218437] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 2.51 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:36:19 +0800] [218047] [INFO] Application startup complete.
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:✅ 预加载完成: Performance768D1M
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   向量数量: 1,000,000
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 2.40 秒
INFO:__main__:   加载时间: 2.37 秒
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 2.32 秒
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   文件大小: 3.10 GB
INFO:__main__:   加载时间: 2.24 秒
INFO:__main__:   加载时间: 2.50 秒
INFO:__main__:   加载时间: 2.58 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:   加载时间: 2.24 秒
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:🎯 预加载完成，总内存使用约: 2.86 GB
INFO:__main__:✅ 应用启动完成
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:💡 所有 Worker 进程将共享这些索引 (Copy-on-Write)
INFO:__main__:✅ 应用启动完成
INFO:__main__:✅ 应用启动完成
INFO:__main__:✅ 应用启动完成
INFO:__main__:✅ 应用启动完成
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:36:19 +0800] [218116] [INFO] Application startup complete.
[2025-07-26 22:36:19 +0800] [218048] [INFO] Application startup complete.
[2025-07-26 22:36:19 +0800] [218260] [INFO] Application startup complete.
[2025-07-26 22:36:19 +0800] [218046] [INFO] Application startup complete.
[2025-07-26 22:36:19 +0800] [218326] [INFO] Application startup complete.
[2025-07-26 22:36:19 +0800] [218435] [INFO] Application startup complete.
[2025-07-26 22:36:19 +0800] [218437] [INFO] Application startup complete.
INFO:__main__:创建索引: 维度=768, 类型=HNSW
INFO:__main__:HNSW参数: M=30, ef_construction=360
INFO:__main__:🔍 检查预加载索引...
INFO:__main__:✅ 使用预加载索引: Performance768D1M
INFO:__main__:🚀 索引就绪: 1,000,000 向量
[2025-07-26 22:40:12 +0800] [217983] [INFO] Handling signal: term
[2025-07-26 22:40:12 +0800] [218046] [INFO] Shutting down
[2025-07-26 22:40:12 +0800] [218116] [INFO] Shutting down
[2025-07-26 22:40:12 +0800] [218435] [INFO] Shutting down
[2025-07-26 22:40:12 +0800] [218326] [INFO] Shutting down
[2025-07-26 22:40:12 +0800] [218437] [INFO] Shutting down
[2025-07-26 22:40:12 +0800] [218260] [INFO] Shutting down
[2025-07-26 22:40:12 +0800] [218047] [INFO] Shutting down
[2025-07-26 22:40:12 +0800] [218048] [INFO] Shutting down
[2025-07-26 22:40:12 +0800] [218116] [INFO] Waiting for application shutdown.
[2025-07-26 22:40:12 +0800] [218046] [INFO] Waiting for application shutdown.
[2025-07-26 22:40:12 +0800] [218116] [INFO] Application shutdown complete.
[2025-07-26 22:40:12 +0800] [218116] [INFO] Finished server process [218116]
[2025-07-26 22:40:12 +0800] [218046] [INFO] Application shutdown complete.
[2025-07-26 22:40:12 +0800] [218046] [INFO] Finished server process [218046]
[2025-07-26 22:40:12 +0800] [218435] [INFO] Waiting for application shutdown.
[2025-07-26 22:40:12 +0800] [218435] [INFO] Application shutdown complete.
[2025-07-26 22:40:12 +0800] [218435] [INFO] Finished server process [218435]
[2025-07-26 22:40:12 +0800] [218326] [INFO] Waiting for application shutdown.
[2025-07-26 22:40:12 +0800] [218326] [INFO] Application shutdown complete.
[2025-07-26 22:40:12 +0800] [218326] [INFO] Finished server process [218326]
[2025-07-26 22:40:12 +0800] [218260] [INFO] Waiting for application shutdown.
[2025-07-26 22:40:12 +0800] [218047] [INFO] Waiting for application shutdown.
[2025-07-26 22:40:12 +0800] [218260] [INFO] Application shutdown complete.
[2025-07-26 22:40:12 +0800] [218047] [INFO] Application shutdown complete.
[2025-07-26 22:40:12 +0800] [218260] [INFO] Finished server process [218260]
[2025-07-26 22:40:12 +0800] [218047] [INFO] Finished server process [218047]
[2025-07-26 22:40:12 +0800] [218437] [INFO] Waiting for application shutdown.
[2025-07-26 22:40:12 +0800] [218437] [INFO] Application shutdown complete.
[2025-07-26 22:40:12 +0800] [218437] [INFO] Finished server process [218437]
[2025-07-26 22:40:12 +0800] [218048] [INFO] Waiting for application shutdown.
[2025-07-26 22:40:12 +0800] [218048] [INFO] Application shutdown complete.
[2025-07-26 22:40:12 +0800] [218048] [INFO] Finished server process [218048]
[2025-07-26 22:40:12 +0800] [217983] [ERROR] Worker (pid:218046) was sent SIGTERM!
--- Logging error ---
[2025-07-26 22:40:12 +0800] [217983] [ERROR] Worker (pid:218326) was sent SIGTERM!
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/gunicorn/arbiter.py", line 223, in run
    handler()
  File "/usr/local/lib/python3.11/site-packages/gunicorn/arbiter.py", line 256, in handle_term
    raise StopIteration
StopIteration

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib64/python3.11/logging/__init__.py", line 1113, in emit
    stream.write(msg + self.terminator)
RuntimeError: reentrant call inside <_io.BufferedWriter name='<stderr>'>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib64/python3.11/logging/__init__.py", line 1113, in emit
    stream.write(msg + self.terminator)
  File "/usr/local/lib/python3.11/site-packages/gunicorn/arbiter.py", line 241, in handle_chld
    self.reap_workers()
  File "/usr/local/lib/python3.11/site-packages/gunicorn/arbiter.py", line 553, in reap_workers
    self.log.error(msg)
  File "/usr/local/lib/python3.11/site-packages/gunicorn/glogging.py", line 271, in error
    self.error_log.error(msg, *args, **kwargs)
  File "/usr/lib64/python3.11/logging/__init__.py", line 1518, in error
    self._log(ERROR, msg, args, **kwargs)
  File "/usr/lib64/python3.11/logging/__init__.py", line 1634, in _log
    self.handle(record)
  File "/usr/lib64/python3.11/logging/__init__.py", line 1644, in handle
    self.callHandlers(record)
  File "/usr/lib64/python3.11/logging/__init__.py", line 1706, in callHandlers
    hdlr.handle(record)
  File "/usr/lib64/python3.11/logging/__init__.py", line 978, in handle
    self.emit(record)
  File "/usr/lib64/python3.11/logging/__init__.py", line 1118, in emit
    self.handleError(record)
  File "/usr/lib64/python3.11/logging/__init__.py", line 1031, in handleError
    sys.stderr.write('--- Logging error ---\n')
RuntimeError: reentrant call inside <_io.BufferedWriter name='<stderr>'>
Call stack:
[2025-07-26 22:40:12 +0800] [217983] [ERROR] Worker (pid:218260) was sent SIGTERM!
[2025-07-26 22:40:12 +0800] [217983] [ERROR] Worker (pid:218047) was sent SIGTERM!
[2025-07-26 22:40:12 +0800] [217983] [ERROR] Worker (pid:218048) was sent SIGTERM!
[2025-07-26 22:40:12 +0800] [217983] [ERROR] Worker (pid:218437) was sent SIGTERM!
  File "/home/<USER>/VectorDBBench/smart_faiss_server.py", line 748, in <module>
    # 🔄 Uvicorn 单进程模式
  File "/home/<USER>/VectorDBBench/smart_faiss_server.py", line 714, in main
    for key, value in self.options.items():
  File "/usr/local/lib/python3.11/site-packages/gunicorn/app/base.py", line 71, in run
    Arbiter(self).run()
  File "/usr/local/lib/python3.11/site-packages/gunicorn/arbiter.py", line 226, in run
    self.halt()
  File "/usr/local/lib/python3.11/site-packages/gunicorn/arbiter.py", line 341, in halt
    self.stop()
  File "/usr/local/lib/python3.11/site-packages/gunicorn/arbiter.py", line 395, in stop
    time.sleep(0.1)
  File "/usr/local/lib/python3.11/site-packages/gunicorn/arbiter.py", line 241, in handle_chld
    self.reap_workers()
  File "/usr/local/lib/python3.11/site-packages/gunicorn/arbiter.py", line 515, in reap_workers
    wpid, status = os.waitpid(-1, os.WNOHANG)
  File "/usr/local/lib/python3.11/site-packages/gunicorn/arbiter.py", line 241, in handle_chld
    self.reap_workers()
  File "/usr/local/lib/python3.11/site-packages/gunicorn/arbiter.py", line 553, in reap_workers
    self.log.error(msg)
  File "/usr/local/lib/python3.11/site-packages/gunicorn/glogging.py", line 271, in error
    self.error_log.error(msg, *args, **kwargs)
Message: 'Worker (pid:218046) was sent SIGTERM!'
Arguments: ()
[2025-07-26 22:40:12 +0800] [217983] [ERROR] Worker (pid:218116) was sent SIGTERM!
[2025-07-26 22:50:12 +0800] [217983] [INFO] Shutting down: Master
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

