nohup: ignoring input
INFO:resource_manager:Applying resource limits: 16 CPU cores, 64.0GB RAM
INFO:resource_manager:CPU affinity set to cores: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]
INFO:resource_manager:Memory limit set to 64.0GB
INFO:__main__:✅ 资源限制已应用: 16核心, 64GB内存
INFO:     Started server process [330055]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 98] error while attempting to bind on address ('0.0.0.0', 8001): address already in use
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8001
� 数据集路径: /nas/yvan.chen/milvus/dataset
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

