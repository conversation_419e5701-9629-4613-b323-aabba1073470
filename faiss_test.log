nohup: ignoring input
INFO:__main__:🧵 使用同步模型 (无线程池) - 避免内存不足问题
INFO:resource_manager:Applying resource limits: 16 CPU cores, 64.0GB RAM
INFO:resource_manager:CPU affinity set to cores: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]
INFO:resource_manager:Memory limit set to 64.0GB
INFO:__main__:✅ 资源限制已应用: 16核心, 64GB内存
INFO:__main__:🧵 FAISS OpenMP线程数设置为: 2
[2025-07-26 21:42:21 +0800] [63954] [INFO] Starting gunicorn 23.0.0
[2025-07-26 21:42:21 +0800] [63954] [INFO] Listening at: http://0.0.0.0:8005 (63954)
[2025-07-26 21:42:21 +0800] [63954] [INFO] Using worker: uvicorn.workers.UvicornWorker
[2025-07-26 21:42:21 +0800] [64283] [INFO] Booting worker with pid: 64283
[2025-07-26 21:42:21 +0800] [64283] [INFO] Started server process [64283]
[2025-07-26 21:42:21 +0800] [64283] [INFO] Waiting for application startup.
[2025-07-26 21:42:21 +0800] [64283] [INFO] Application startup complete.
[2025-07-26 21:42:21 +0800] [64289] [INFO] Booting worker with pid: 64289
[2025-07-26 21:42:21 +0800] [64289] [INFO] Started server process [64289]
[2025-07-26 21:42:21 +0800] [64289] [INFO] Waiting for application startup.
[2025-07-26 21:42:21 +0800] [64289] [INFO] Application startup complete.
[2025-07-26 21:42:21 +0800] [64300] [INFO] Booting worker with pid: 64300
[2025-07-26 21:42:21 +0800] [64300] [INFO] Started server process [64300]
[2025-07-26 21:42:21 +0800] [64300] [INFO] Waiting for application startup.
[2025-07-26 21:42:21 +0800] [64300] [INFO] Application startup complete.
[2025-07-26 21:42:21 +0800] [64301] [INFO] Booting worker with pid: 64301
[2025-07-26 21:42:21 +0800] [64301] [INFO] Started server process [64301]
[2025-07-26 21:42:21 +0800] [64301] [INFO] Waiting for application startup.
[2025-07-26 21:42:21 +0800] [64301] [INFO] Application startup complete.
[2025-07-26 21:42:21 +0800] [64302] [INFO] Booting worker with pid: 64302
[2025-07-26 21:42:21 +0800] [64302] [INFO] Started server process [64302]
[2025-07-26 21:42:21 +0800] [64302] [INFO] Waiting for application startup.
[2025-07-26 21:42:21 +0800] [64302] [INFO] Application startup complete.
[2025-07-26 21:42:21 +0800] [64303] [INFO] Booting worker with pid: 64303
[2025-07-26 21:42:21 +0800] [64303] [INFO] Started server process [64303]
[2025-07-26 21:42:21 +0800] [64303] [INFO] Waiting for application startup.
[2025-07-26 21:42:21 +0800] [64303] [INFO] Application startup complete.
[2025-07-26 21:42:21 +0800] [64304] [INFO] Booting worker with pid: 64304
[2025-07-26 21:42:21 +0800] [64304] [INFO] Started server process [64304]
[2025-07-26 21:42:21 +0800] [64304] [INFO] Waiting for application startup.
[2025-07-26 21:42:21 +0800] [64304] [INFO] Application startup complete.
[2025-07-26 21:42:22 +0800] [64309] [INFO] Booting worker with pid: 64309
[2025-07-26 21:42:22 +0800] [64309] [INFO] Started server process [64309]
[2025-07-26 21:42:22 +0800] [64309] [INFO] Waiting for application startup.
[2025-07-26 21:42:22 +0800] [64309] [INFO] Application startup complete.
INFO:__main__:创建索引: 维度=768, 类型=HNSW
INFO:__main__:HNSW参数: M=30, ef_construction=360
INFO:__main__:🚀 查找匹配的真实数据集...
INFO:__main__:✅ 找到匹配数据集: Performance768D1M (/nas/yvan.chen/milvus/dataset/cohere/cohere_medium_1m)
INFO:__main__:📁 加载数据集: cohere/cohere_medium_1m
INFO:__main__:📊 正在加载 1 个训练文件...
INFO:__main__:📄 读取文件: shuffle_train.parquet
INFO:__main__:📊 已加载 50,000 个向量
INFO:__main__:📊 已加载 100,000 个向量
INFO:__main__:📊 已加载 150,000 个向量
INFO:__main__:📊 已加载 200,000 个向量
INFO:__main__:📊 已加载 250,000 个向量
INFO:__main__:📊 已加载 300,000 个向量
INFO:__main__:📊 已加载 350,000 个向量
[2025-07-26 21:52:22 +0800] [63954] [CRITICAL] WORKER TIMEOUT (pid:64309)
[2025-07-26 21:52:22 +0800] [63954] [ERROR] Worker (pid:64309) was sent code 134!
[2025-07-26 21:52:22 +0800] [326698] [INFO] Booting worker with pid: 326698
[2025-07-26 21:52:22 +0800] [326698] [INFO] Started server process [326698]
[2025-07-26 21:52:22 +0800] [326698] [INFO] Waiting for application startup.
[2025-07-26 21:52:22 +0800] [326698] [INFO] Application startup complete.
[2025-07-26 21:54:44 +0800] [63954] [INFO] Handling signal: term
[2025-07-26 21:54:44 +0800] [64304] [INFO] Shutting down
[2025-07-26 21:54:44 +0800] [64303] [INFO] Shutting down
[2025-07-26 21:54:44 +0800] [64283] [INFO] Shutting down
[2025-07-26 21:54:44 +0800] [64289] [INFO] Shutting down
[2025-07-26 21:54:44 +0800] [64302] [INFO] Shutting down
[2025-07-26 21:54:44 +0800] [64300] [INFO] Shutting down
[2025-07-26 21:54:44 +0800] [64301] [INFO] Shutting down
[2025-07-26 21:54:44 +0800] [326698] [INFO] Shutting down
[2025-07-26 21:54:44 +0800] [64304] [INFO] Waiting for application shutdown.
[2025-07-26 21:54:44 +0800] [64304] [INFO] Application shutdown complete.
[2025-07-26 21:54:44 +0800] [64304] [INFO] Finished server process [64304]
[2025-07-26 21:54:44 +0800] [63954] [ERROR] Worker (pid:64304) was sent SIGTERM!
[2025-07-26 21:54:44 +0800] [64303] [INFO] Waiting for application shutdown.
[2025-07-26 21:54:44 +0800] [64303] [INFO] Application shutdown complete.
[2025-07-26 21:54:44 +0800] [64303] [INFO] Finished server process [64303]
[2025-07-26 21:54:44 +0800] [64283] [INFO] Waiting for application shutdown.
[2025-07-26 21:54:44 +0800] [64283] [INFO] Application shutdown complete.
[2025-07-26 21:54:44 +0800] [64283] [INFO] Finished server process [64283]
[2025-07-26 21:54:44 +0800] [63954] [ERROR] Worker (pid:64303) was sent SIGTERM!
[2025-07-26 21:54:44 +0800] [63954] [ERROR] Worker (pid:64283) was sent SIGTERM!
[2025-07-26 21:54:44 +0800] [64289] [INFO] Waiting for application shutdown.
[2025-07-26 21:54:44 +0800] [64289] [INFO] Application shutdown complete.
[2025-07-26 21:54:44 +0800] [64289] [INFO] Finished server process [64289]
[2025-07-26 21:54:44 +0800] [64302] [INFO] Waiting for application shutdown.
[2025-07-26 21:54:44 +0800] [64302] [INFO] Application shutdown complete.
[2025-07-26 21:54:44 +0800] [64302] [INFO] Finished server process [64302]
[2025-07-26 21:54:44 +0800] [63954] [ERROR] Worker (pid:64289) was sent SIGTERM!
[2025-07-26 21:54:44 +0800] [64300] [INFO] Waiting for application shutdown.
[2025-07-26 21:54:44 +0800] [64300] [INFO] Application shutdown complete.
[2025-07-26 21:54:44 +0800] [64300] [INFO] Finished server process [64300]
[2025-07-26 21:54:44 +0800] [63954] [ERROR] Worker (pid:64302) was sent SIGTERM!
[2025-07-26 21:54:44 +0800] [63954] [ERROR] Worker (pid:64300) was sent SIGTERM!
[2025-07-26 21:54:44 +0800] [64301] [INFO] Waiting for application shutdown.
[2025-07-26 21:54:44 +0800] [64301] [INFO] Application shutdown complete.
[2025-07-26 21:54:44 +0800] [64301] [INFO] Finished server process [64301]
[2025-07-26 21:54:44 +0800] [326698] [INFO] Waiting for application shutdown.
[2025-07-26 21:54:44 +0800] [326698] [INFO] Application shutdown complete.
[2025-07-26 21:54:44 +0800] [326698] [INFO] Finished server process [326698]
[2025-07-26 21:54:44 +0800] [63954] [ERROR] Worker (pid:64301) was sent SIGTERM!
[2025-07-26 21:54:44 +0800] [63954] [ERROR] Worker (pid:326698) was sent SIGTERM!
[2025-07-26 21:54:44 +0800] [63954] [INFO] Shutting down: Master
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

