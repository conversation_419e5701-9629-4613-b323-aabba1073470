#!/usr/bin/env python3
"""
修复512并发的系统资源限制问题
"""

import os
import sys
import subprocess
import psutil
import time
import threading
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor

def check_system_limits():
    """检查系统限制"""
    print("🔍 检查系统资源限制")
    print("=" * 50)
    
    # 1. 线程限制
    try:
        result = subprocess.run(['cat', '/proc/sys/kernel/threads-max'], capture_output=True, text=True)
        max_threads = result.stdout.strip()
        print(f"📊 系统最大线程数: {max_threads}")
    except:
        print("❌ 无法获取系统线程限制")
    
    # 2. 进程限制
    try:
        result = subprocess.run(['bash', '-c', 'ulimit -u'], capture_output=True, text=True)
        max_processes = result.stdout.strip()
        print(f"📊 用户最大进程数: {max_processes}")
    except:
        print("❌ 无法获取进程限制")
    
    # 3. 文件描述符限制
    try:
        result = subprocess.run(['bash', '-c', 'ulimit -n'], capture_output=True, text=True)
        max_files = result.stdout.strip()
        print(f"📊 最大文件描述符: {max_files}")
    except:
        print("❌ 无法获取文件描述符限制")
    
    # 4. 当前资源使用
    current_processes = len(psutil.pids())
    current_threads = sum(p.num_threads() for p in psutil.process_iter(['num_threads']) if p.info['num_threads'])
    
    print(f"📊 当前进程数: {current_processes}")
    print(f"📊 当前线程数: {current_threads}")
    
    # 5. 内存使用
    memory = psutil.virtual_memory()
    print(f"📊 内存使用: {memory.percent}% ({memory.used // (1024**3)}GB / {memory.total // (1024**3)}GB)")
    
    return {
        'current_processes': current_processes,
        'current_threads': current_threads,
        'memory_percent': memory.percent
    }

def cleanup_zombie_processes():
    """清理僵尸进程"""
    print("\n🧹 清理僵尸进程")
    print("=" * 50)
    
    zombie_count = 0
    cleaned_count = 0
    
    for proc in psutil.process_iter(['pid', 'status', 'name']):
        try:
            if proc.info['status'] == psutil.STATUS_ZOMBIE:
                zombie_count += 1
                print(f"🧟 发现僵尸进程: PID={proc.info['pid']}, Name={proc.info['name']}")
                try:
                    proc.terminate()
                    cleaned_count += 1
                except:
                    pass
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
    
    if zombie_count == 0:
        print("✅ 没有发现僵尸进程")
    else:
        print(f"📊 发现 {zombie_count} 个僵尸进程，清理了 {cleaned_count} 个")

def test_thread_creation_limit():
    """测试线程创建限制"""
    print("\n🧪 测试线程创建限制")
    print("=" * 50)
    
    threads = []
    max_threads = 0
    
    def dummy_thread():
        time.sleep(1)
    
    try:
        for i in range(10000):  # 尝试创建大量线程
            try:
                t = threading.Thread(target=dummy_thread)
                t.start()
                threads.append(t)
                max_threads = i + 1
                
                if (i + 1) % 100 == 0:
                    print(f"   已创建 {i + 1} 个线程")
                    
            except RuntimeError as e:
                if "can't start new thread" in str(e):
                    print(f"❌ 线程创建失败在 {i + 1} 个线程: {e}")
                    break
                else:
                    raise
    finally:
        # 清理线程
        for t in threads:
            try:
                t.join(timeout=0.1)
            except:
                pass
    
    print(f"📊 最大可创建线程数: {max_threads}")
    return max_threads

def test_process_creation_limit():
    """测试进程创建限制"""
    print("\n🧪 测试进程创建限制")
    print("=" * 50)
    
    def dummy_worker(x):
        time.sleep(0.1)
        return x
    
    max_processes = 0
    
    # 逐步增加进程数
    for proc_count in [50, 100, 200, 300, 400, 500, 512, 600, 700, 800, 1000]:
        print(f"   测试 {proc_count} 个进程...")
        
        try:
            start_time = time.time()
            
            with ProcessPoolExecutor(
                max_workers=proc_count,
                mp_context=mp.get_context("spawn")
            ) as executor:
                
                # 提交少量任务测试
                futures = [executor.submit(dummy_worker, i) for i in range(min(10, proc_count))]
                
                # 等待完成
                completed = 0
                for future in futures:
                    try:
                        future.result(timeout=5)
                        completed += 1
                    except:
                        pass
                
                elapsed = time.time() - start_time
                
                if completed == len(futures):
                    print(f"   ✅ {proc_count} 进程成功 ({elapsed:.2f}s)")
                    max_processes = proc_count
                else:
                    print(f"   ❌ {proc_count} 进程失败")
                    break
                    
        except Exception as e:
            print(f"   ❌ {proc_count} 进程异常: {e}")
            break
    
    print(f"📊 最大可创建进程数: {max_processes}")
    return max_processes

def increase_system_limits():
    """尝试增加系统限制"""
    print("\n🔧 尝试增加系统限制")
    print("=" * 50)
    
    commands = [
        # 增加用户进程限制
        "ulimit -u 65536",
        # 增加文件描述符限制
        "ulimit -n 65536",
        # 增加线程栈大小
        "ulimit -s 8192",
    ]
    
    for cmd in commands:
        try:
            result = subprocess.run(['bash', '-c', cmd], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ 执行成功: {cmd}")
            else:
                print(f"❌ 执行失败: {cmd} - {result.stderr}")
        except Exception as e:
            print(f"❌ 执行异常: {cmd} - {e}")

def suggest_optimizations():
    """建议优化方案"""
    print("\n💡 512并发优化建议")
    print("=" * 50)
    
    print("🔧 系统级优化:")
    print("1. 增加系统线程限制:")
    print("   echo 'kernel.threads-max = 1000000' >> /etc/sysctl.conf")
    print("   sysctl -p")
    
    print("\n2. 增加用户进程限制:")
    print("   echo '* soft nproc 65536' >> /etc/security/limits.conf")
    print("   echo '* hard nproc 65536' >> /etc/security/limits.conf")
    
    print("\n3. 增加文件描述符限制:")
    print("   echo '* soft nofile 65536' >> /etc/security/limits.conf")
    print("   echo '* hard nofile 65536' >> /etc/security/limits.conf")
    
    print("\n🏗️ VectorDBBench优化:")
    print("1. 减少并发数到安全范围 (256-384)")
    print("2. 使用批量处理减少进程数")
    print("3. 优化multiprocessing.Manager使用")
    
    print("\n⚡ 立即可用的解决方案:")
    print("1. 重启系统释放资源")
    print("2. 使用 256 并发替代 512")
    print("3. 分批次运行测试")

def create_safe_512_test():
    """创建安全的512并发测试"""
    print("\n🛡️ 创建安全的512并发测试")
    print("=" * 50)
    
    script_content = '''#!/bin/bash
# 安全的512并发测试脚本

echo "🔧 设置资源限制..."
ulimit -u 65536
ulimit -n 65536
ulimit -s 8192

echo "🧹 清理环境..."
# 杀死可能残留的进程
pkill -f vectordbbench || true
sleep 2

echo "📊 检查资源状态..."
echo "当前进程数: $(ps aux | wc -l)"
echo "当前线程数: $(ps -eLf | wc -l)"

echo "🚀 启动512并发测试..."
cd /home/<USER>/VectorDBBench
export DATASET_LOCAL_DIR=/nas/yvan.chen/milvus/dataset

# 使用较短的测试时间
python3.11 -m vectordb_bench.cli.vectordbbench faissremote \\
    --uri http://***********:8005 \\
    --case-type Performance768D10M \\
    --index-type HNSW \\
    --m 30 \\
    --ef-construction 360 \\
    --concurrency-duration 10 \\
    --num-concurrency 512 \\
    --skip-load \\
    --skip-search-serial

echo "✅ 测试完成"
'''
    
    with open('safe_512_test.sh', 'w') as f:
        f.write(script_content)
    
    os.chmod('safe_512_test.sh', 0o755)
    print("✅ 创建了安全测试脚本: safe_512_test.sh")
    print("💡 使用方法: ./safe_512_test.sh")

def main():
    print("🔧 VectorDBBench 512并发资源限制修复工具")
    print("=" * 60)
    
    # 1. 检查系统限制
    stats = check_system_limits()
    
    # 2. 清理僵尸进程
    cleanup_zombie_processes()
    
    # 3. 测试线程创建限制
    max_threads = test_thread_creation_limit()
    
    # 4. 测试进程创建限制
    max_processes = test_process_creation_limit()
    
    # 5. 尝试增加限制
    increase_system_limits()
    
    # 6. 生成建议
    suggest_optimizations()
    
    # 7. 创建安全测试脚本
    create_safe_512_test()
    
    print(f"\n" + "=" * 60)
    print(f"📋 诊断总结")
    print(f"=" * 60)
    
    print(f"📊 资源状态:")
    print(f"   当前进程数: {stats['current_processes']}")
    print(f"   当前线程数: {stats['current_threads']}")
    print(f"   内存使用率: {stats['memory_percent']}%")
    print(f"   最大线程数: {max_threads}")
    print(f"   最大进程数: {max_processes}")
    
    if max_processes >= 512:
        print(f"\n✅ 系统支持512并发，问题可能是临时的")
        print(f"💡 建议使用 safe_512_test.sh 重新测试")
    else:
        print(f"\n❌ 系统不支持512并发")
        print(f"💡 建议使用 {max_processes} 作为最大并发数")

if __name__ == "__main__":
    main()
