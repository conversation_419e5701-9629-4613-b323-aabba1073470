#!/bin/bash
# 彻底解决FAISS服务器连接重置问题

echo "🔧 修复FAISS连接重置问题"
echo "=========================="

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "⚠️  需要root权限来修改系统配置"
    echo "请使用: sudo $0"
    exit 1
fi

echo "📊 当前系统限制:"
echo "   文件描述符软限制: $(ulimit -Sn)"
echo "   文件描述符硬限制: $(ulimit -Hn)"
echo "   TCP连接队列: $(cat /proc/sys/net/core/somaxconn)"
echo "   网络缓冲区: $(cat /proc/sys/net/core/netdev_max_backlog)"

echo ""
echo "🔧 应用系统级修复..."

# 1. 大幅增加文件描述符限制
echo "🔧 增加文件描述符限制..."
echo "* soft nofile 1048576" > /etc/security/limits.d/99-faiss.conf
echo "* hard nofile 1048576" >> /etc/security/limits.d/99-faiss.conf
echo "root soft nofile 1048576" >> /etc/security/limits.d/99-faiss.conf
echo "root hard nofile 1048576" >> /etc/security/limits.d/99-faiss.conf

# 2. 优化TCP参数 - 专门解决连接重置
echo "🔧 优化TCP参数..."
cat > /etc/sysctl.d/99-faiss-network.conf << 'EOF'
# FAISS高并发网络优化
net.core.somaxconn = 32768
net.core.netdev_max_backlog = 32768
net.core.rmem_default = 262144
net.core.rmem_max = 16777216
net.core.wmem_default = 262144
net.core.wmem_max = 16777216

# TCP优化
net.ipv4.tcp_rmem = 4096 87380 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
net.ipv4.tcp_congestion_control = bbr
net.ipv4.tcp_slow_start_after_idle = 0

# 连接复用和回收
net.ipv4.tcp_tw_reuse = 1
net.ipv4.tcp_fin_timeout = 10
net.ipv4.tcp_keepalive_time = 600
net.ipv4.tcp_keepalive_probes = 3
net.ipv4.tcp_keepalive_intvl = 15

# 端口范围
net.ipv4.ip_local_port_range = 1024 65535

# 防止连接重置
net.ipv4.tcp_abort_on_overflow = 0
net.ipv4.tcp_syncookies = 1
net.ipv4.tcp_max_syn_backlog = 8192

# 内存优化
vm.swappiness = 1
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5
EOF

# 应用sysctl配置
sysctl -p /etc/sysctl.d/99-faiss-network.conf

# 3. 设置当前会话的限制
echo "🔧 设置当前会话限制..."
ulimit -n 1048576

# 4. 优化systemd服务限制 (如果使用systemd)
if command -v systemctl &> /dev/null; then
    echo "🔧 优化systemd限制..."
    mkdir -p /etc/systemd/system.conf.d
    cat > /etc/systemd/system.conf.d/99-faiss.conf << 'EOF'
[Manager]
DefaultLimitNOFILE=1048576
DefaultLimitNPROC=1048576
EOF
    systemctl daemon-reload
fi

# 5. 创建FAISS专用启动脚本
echo "🔧 创建优化启动脚本..."
cat > /home/<USER>/VectorDBBench/start_faiss_stable.sh << 'EOF'
#!/bin/bash
# FAISS稳定启动脚本 - 解决连接问题

echo "🚀 启动稳定版FAISS服务器"
echo "========================"

# 设置资源限制
ulimit -n 1048576
ulimit -u 1048576

# 设置环境变量
export DATASET_LOCAL_DIR=/nas/yvan.chen/milvus/dataset
export PYTHONPATH=/home/<USER>/VectorDBBench:$PYTHONPATH

# 停止现有服务
pkill -f smart_faiss_server 2>/dev/null || true
sleep 3

# 获取时间戳
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

echo "🔧 环境配置:"
echo "   文件描述符限制: $(ulimit -n)"
echo "   进程限制: $(ulimit -u)"
echo "   TCP连接队列: $(cat /proc/sys/net/core/somaxconn)"

# 启动服务
echo ""
echo "🚀 启动FAISS服务 (稳定配置)..."
nohup numactl -C 0-15 python3.11 smart_faiss_server.py \
    --host 0.0.0.0 \
    --port 8005 \
    --use-gunicorn \
    --workers 8 \
    --preload \
    > faiss_stable_${TIMESTAMP}.log 2>&1 &

FAISS_PID=$!
echo "✅ FAISS服务启动完成"
echo "   主进程PID: $FAISS_PID"
echo "   日志文件: faiss_stable_${TIMESTAMP}.log"

# 等待服务启动
echo ""
echo "⏳ 等待服务启动..."
for i in {1..30}; do
    if curl -s http://localhost:8005/status > /dev/null 2>&1; then
        echo "✅ FAISS服务运行正常 (耗时: ${i}秒)"
        break
    fi
    sleep 1
done

echo ""
echo "📋 服务信息:"
echo "   服务地址: http://0.0.0.0:8005"
echo "   主进程PID: $FAISS_PID"
echo "   日志文件: faiss_stable_${TIMESTAMP}.log"
echo ""
echo "🔧 管理命令:"
echo "   查看日志: tail -f faiss_stable_${TIMESTAMP}.log"
echo "   查看进程: ps aux | grep smart_faiss_server"
echo "   停止服务: kill $FAISS_PID"
echo "   API测试: curl http://localhost:8005/status"
EOF

chmod +x /home/<USER>/VectorDBBench/start_faiss_stable.sh
chown brian:brian /home/<USER>/VectorDBBench/start_faiss_stable.sh

echo ""
echo "✅ 系统优化完成！"
echo ""
echo "📊 优化后配置:"
echo "   文件描述符限制: $(ulimit -n)"
echo "   TCP连接队列: $(cat /proc/sys/net/core/somaxconn)"
echo "   网络缓冲区: $(cat /proc/sys/net/core/netdev_max_backlog)"
echo ""
echo "🚀 使用新的稳定启动脚本:"
echo "   cd /home/<USER>/VectorDBBench"
echo "   ./start_faiss_stable.sh"
echo ""
echo "💡 建议:"
echo "   1. 重启系统以确保所有配置生效"
echo "   2. 使用新的启动脚本"
echo "   3. 监控连接错误是否减少"
