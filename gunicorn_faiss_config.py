# Gunicorn 配置文件 - FAISS 服务器优化配置
# 使用 preload_app 避免 FAISS 索引重复加载导致的内存爆炸

import multiprocessing
import os

# 🔗 绑定地址和端口
bind = "0.0.0.0:8005"

# 🚀 Worker 进程配置
workers = min(8, multiprocessing.cpu_count())  # 最多8个进程，不超过CPU核心数
worker_class = "uvicorn.workers.UvicornWorker"  # 使用 Uvicorn Worker

# 🔑 关键配置：预加载应用
preload_app = True  # 在主进程中预加载应用和 FAISS 索引，然后 fork 到 worker

# 📊 性能优化配置
max_requests = 2000           # 每个 worker 处理的最大请求数
max_requests_jitter = 100     # 随机抖动，避免所有 worker 同时重启
timeout = 120                 # 请求超时时间
keepalive = 5                 # HTTP keep-alive 时间
worker_connections = 1000     # 每个 worker 的最大并发连接数

# 🔧 进程管理
graceful_timeout = 60         # 优雅关闭超时
worker_tmp_dir = "/dev/shm"   # 使用内存文件系统存储临时文件

# 📝 日志配置
loglevel = "info"
accesslog = "-"               # 访问日志输出到 stdout
errorlog = "-"                # 错误日志输出到 stderr
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# 🔒 安全配置
limit_request_line = 8192     # 请求行最大长度
limit_request_fields = 100    # 请求头字段数量限制
limit_request_field_size = 8192  # 请求头字段大小限制

# 🧵 线程配置 (对 UvicornWorker 无效，但保留以备将来使用)
threads = 1

# 📈 监控配置
statsd_host = None            # StatsD 监控地址 (可选)
proc_name = "faiss_server"    # 进程名称

# 🔄 重启配置
reload = False                # 生产环境不启用自动重载
reload_engine = "auto"

# 🌐 网络配置
backlog = 2048               # 监听队列大小

def when_ready(server):
    """服务器启动完成时的回调"""
    print("🚀 FAISS 服务器启动完成")
    print(f"   绑定地址: {bind}")
    print(f"   Worker 进程数: {workers}")
    print(f"   预加载模式: {preload_app}")
    print(f"   最大并发连接: {workers * worker_connections}")

def worker_int(worker):
    """Worker 进程中断时的回调"""
    print(f"⚠️  Worker {worker.pid} 收到中断信号")

def pre_fork(server, worker):
    """Fork worker 之前的回调"""
    print(f"🔄 正在启动 Worker {worker.age}")

def post_fork(server, worker):
    """Fork worker 之后的回调"""
    print(f"✅ Worker {worker.pid} 启动完成")

def worker_abort(worker):
    """Worker 异常退出时的回调"""
    print(f"❌ Worker {worker.pid} 异常退出")

# 🔧 环境变量设置
def on_starting(server):
    """服务器启动时设置环境变量"""
    # FAISS 多线程优化
    os.environ.setdefault('OMP_NUM_THREADS', '2')  # 每个 worker 使用 2 个 OpenMP 线程
    os.environ.setdefault('MKL_NUM_THREADS', '2')
    os.environ.setdefault('OPENBLAS_NUM_THREADS', '2')
    os.environ.setdefault('VECLIB_MAXIMUM_THREADS', '2')
    
    print("🧵 设置 FAISS 多线程环境变量:")
    print(f"   OMP_NUM_THREADS: {os.environ.get('OMP_NUM_THREADS')}")
    print(f"   总线程数预期: {workers} workers × 2 threads = {workers * 2} threads")

# 📊 资源限制
def post_worker_init(worker):
    """Worker 初始化完成后的回调"""
    import resource
    
    # 设置内存限制 (可选)
    # soft, hard = resource.getrlimit(resource.RLIMIT_AS)
    # resource.setrlimit(resource.RLIMIT_AS, (8 * 1024 * 1024 * 1024, hard))  # 8GB 限制
    
    print(f"🔧 Worker {worker.pid} 资源配置完成")
