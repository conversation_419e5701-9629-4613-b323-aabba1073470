#!/usr/bin/env python3
"""
内存带宽测试工具
"""

import numpy as np
import time
import psutil
import os
import threading
from typing import Dict, List

def measure_memory_bandwidth(array_size_mb: int = 1000, num_iterations: int = 10) -> Dict:
    """
    测量内存带宽
    """
    print(f"🔍 测量内存带宽 (数组大小: {array_size_mb}MB, 迭代次数: {num_iterations})")
    
    # 创建测试数组
    array_size = array_size_mb * 1024 * 1024 // 8  # 8字节的double
    
    # 分配内存
    print("📦 分配内存...")
    a = np.random.random(array_size).astype(np.float64)
    b = np.random.random(array_size).astype(np.float64)
    c = np.zeros(array_size, dtype=np.float64)
    
    print(f"✅ 已分配 {array_size_mb * 3}MB 内存")
    
    # 预热
    print("🔥 预热...")
    for _ in range(3):
        c[:] = a + b
    
    # 测量不同操作的带宽
    results = {}
    
    # 1. Copy操作 (读+写)
    print("📋 测试Copy操作...")
    start_time = time.time()
    for _ in range(num_iterations):
        c[:] = a
    end_time = time.time()
    
    copy_time = end_time - start_time
    copy_bandwidth = (array_size_mb * 2 * num_iterations) / copy_time  # 读+写
    results['copy'] = {
        'bandwidth_mb_s': copy_bandwidth,
        'time_s': copy_time
    }
    
    # 2. Scale操作 (读+写)
    print("⚖️ 测试Scale操作...")
    start_time = time.time()
    for _ in range(num_iterations):
        c[:] = 2.0 * a
    end_time = time.time()
    
    scale_time = end_time - start_time
    scale_bandwidth = (array_size_mb * 2 * num_iterations) / scale_time
    results['scale'] = {
        'bandwidth_mb_s': scale_bandwidth,
        'time_s': scale_time
    }
    
    # 3. Add操作 (读+读+写)
    print("➕ 测试Add操作...")
    start_time = time.time()
    for _ in range(num_iterations):
        c[:] = a + b
    end_time = time.time()
    
    add_time = end_time - start_time
    add_bandwidth = (array_size_mb * 3 * num_iterations) / add_time  # 读a+读b+写c
    results['add'] = {
        'bandwidth_mb_s': add_bandwidth,
        'time_s': add_time
    }
    
    # 4. Triad操作 (读+读+写)
    print("🔺 测试Triad操作...")
    start_time = time.time()
    for _ in range(num_iterations):
        c[:] = a + 2.0 * b
    end_time = time.time()
    
    triad_time = end_time - start_time
    triad_bandwidth = (array_size_mb * 3 * num_iterations) / triad_time
    results['triad'] = {
        'bandwidth_mb_s': triad_bandwidth,
        'time_s': triad_time
    }
    
    return results

def measure_numa_bandwidth():
    """
    测量NUMA节点的内存带宽
    """
    print("🌐 NUMA内存带宽测试")
    print("=" * 50)
    
    # 绑定到NUMA Node 0
    os.system("numactl --cpunodebind=0 --membind=0 echo '绑定到NUMA Node 0'")
    
    # 测量不同大小的内存带宽
    sizes = [100, 500, 1000, 2000]  # MB
    
    for size in sizes:
        print(f"\n📊 测试数组大小: {size}MB")
        print("-" * 30)
        
        results = measure_memory_bandwidth(size, 5)
        
        print(f"Copy:  {results['copy']['bandwidth_mb_s']:.1f} MB/s")
        print(f"Scale: {results['scale']['bandwidth_mb_s']:.1f} MB/s")
        print(f"Add:   {results['add']['bandwidth_mb_s']:.1f} MB/s")
        print(f"Triad: {results['triad']['bandwidth_mb_s']:.1f} MB/s")

def monitor_faiss_memory_bandwidth(duration: int = 30):
    """
    监控FAISS服务器的内存带宽使用
    """
    print(f"📈 监控FAISS服务器内存使用 ({duration}秒)")
    
    # 找到FAISS进程
    faiss_pid = None
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if 'smart_faiss_server.py' in ' '.join(proc.info['cmdline'] or []):
                faiss_pid = proc.info['pid']
                break
        except:
            continue
    
    if not faiss_pid:
        print("❌ 未找到FAISS服务器进程")
        return
    
    print(f"🔍 监控进程 PID: {faiss_pid}")
    faiss_process = psutil.Process(faiss_pid)
    
    # 监控内存使用
    start_time = time.time()
    memory_samples = []
    io_samples = []
    
    while time.time() - start_time < duration:
        try:
            # 内存信息
            memory_info = faiss_process.memory_info()
            memory_gb = memory_info.rss / 1024 / 1024 / 1024
            
            # I/O信息
            io_info = faiss_process.io_counters()
            
            memory_samples.append(memory_gb)
            io_samples.append({
                'read_bytes': io_info.read_bytes,
                'write_bytes': io_info.write_bytes,
                'timestamp': time.time()
            })
            
            print(f"   内存: {memory_gb:.1f}GB, 读: {io_info.read_bytes/1024/1024:.1f}MB, 写: {io_info.write_bytes/1024/1024:.1f}MB")
            
            time.sleep(1)
        except psutil.NoSuchProcess:
            print("❌ 进程已退出")
            break
        except Exception as e:
            print(f"⚠️ 监控错误: {e}")
    
    # 计算I/O带宽
    if len(io_samples) > 1:
        first_sample = io_samples[0]
        last_sample = io_samples[-1]
        
        time_diff = last_sample['timestamp'] - first_sample['timestamp']
        read_diff = last_sample['read_bytes'] - first_sample['read_bytes']
        write_diff = last_sample['write_bytes'] - first_sample['write_bytes']
        
        read_bandwidth = read_diff / time_diff / 1024 / 1024  # MB/s
        write_bandwidth = write_diff / time_diff / 1024 / 1024  # MB/s
        
        print(f"\n📊 I/O带宽统计:")
        print(f"   读带宽: {read_bandwidth:.1f} MB/s")
        print(f"   写带宽: {write_bandwidth:.1f} MB/s")
        print(f"   总带宽: {read_bandwidth + write_bandwidth:.1f} MB/s")

def calculate_theoretical_bandwidth():
    """
    计算理论内存带宽
    """
    print("🧮 计算理论内存带宽")
    print("=" * 30)
    
    # DDR5-5600的理论带宽
    # 5600 MT/s = 5600 * 8 bytes = 44.8 GB/s per channel
    # 通常有多个通道
    
    print("💾 内存规格:")
    print("   类型: DDR5")
    print("   速度: 5600 MT/s")
    print("   单通道理论带宽: 44.8 GB/s")
    
    # 假设是双通道或四通道
    channels = 4  # 通常服务器有4个内存通道
    theoretical_bandwidth = 44.8 * channels
    
    print(f"   假设通道数: {channels}")
    print(f"   理论总带宽: {theoretical_bandwidth:.1f} GB/s")
    print(f"   实际可用带宽: {theoretical_bandwidth * 0.7:.1f} GB/s (70%效率)")

def main():
    print("🧪 内存带宽分析工具")
    print("=" * 60)
    
    # 1. 计算理论带宽
    calculate_theoretical_bandwidth()
    print()
    
    # 2. 测量实际带宽
    measure_numa_bandwidth()
    print()
    
    # 3. 监控FAISS服务器
    print("🔍 开始监控FAISS服务器...")
    monitor_faiss_memory_bandwidth(30)

if __name__ == "__main__":
    main()
