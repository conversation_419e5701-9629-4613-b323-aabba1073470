#!/usr/bin/env python3
"""
内存优化的预加载函数 - 彻底解决重复加载问题
"""

import os
import time
import faiss
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

# 🔒 全局锁和状态
_preload_lock = False
_master_process_pid = None
PRELOADED_INDEXES = {}

def is_master_process():
    """检查是否为主进程"""
    global _master_process_pid
    current_pid = os.getpid()
    
    if _master_process_pid is None:
        _master_process_pid = current_pid
        return True
    
    return current_pid == _master_process_pid

def preload_common_indexes_optimized(index_dir: str) -> Dict[str, Any]:
    """
    🔧 内存优化的预加载函数
    
    关键修复：
    1. 只在主进程中执行一次
    2. 检测并复用软链接指向的相同文件
    3. 强制垃圾回收
    4. 详细的内存监控
    """
    global PRELOADED_INDEXES, _preload_lock
    
    # 🔒 确保只执行一次
    if _preload_lock:
        logger.info("⚠️ 预加载已完成，跳过重复执行")
        return PRELOADED_INDEXES
    
    # 🔍 进程检查
    current_pid = os.getpid()
    parent_pid = os.getppid()
    is_master = is_master_process()
    
    logger.info(f"🔍 进程检查: PID={current_pid}, PPID={parent_pid}, Master={is_master}")
    
    if not is_master:
        logger.info("⚠️ 非主进程，跳过预加载")
        return PRELOADED_INDEXES
    
    _preload_lock = True
    
    # 检查目录
    if not os.path.exists(index_dir):
        logger.warning(f"📁 索引目录不存在: {index_dir}")
        return {}
    
    logger.info("🚀 开始内存优化预加载...")
    
    # 🔑 关键：跟踪真实文件路径，避免重复加载
    loaded_files = {}  # real_path -> (case_name, index_info)
    
    index_files = {
        "Performance768D1M": "faiss_hnsw_768d_1m.index",
        "Performance768D10M": "faiss_hnsw_768d_10m.index"
    }
    
    total_memory_saved = 0
    
    for case_name, index_file in index_files.items():
        index_path = os.path.join(index_dir, index_file)
        
        if not os.path.exists(index_path):
            logger.warning(f"⚠️ 索引文件不存在: {index_path}")
            continue
        
        # 🔍 获取真实文件路径（解析软链接）
        real_path = os.path.realpath(index_path)
        is_symlink = os.path.islink(index_path)
        file_size_gb = os.path.getsize(real_path) / (1024**3)
        
        logger.info(f"🔍 {case_name}:")
        logger.info(f"   符号链接: {index_path}")
        logger.info(f"   真实路径: {real_path}")
        logger.info(f"   是软链接: {is_symlink}")
        logger.info(f"   文件大小: {file_size_gb:.2f} GB")
        
        if real_path in loaded_files:
            # 🎯 关键优化：复用已加载的索引
            existing_case, existing_info = loaded_files[real_path]
            logger.info(f"♻️ 内存优化: {case_name} 复用 {existing_case} 的索引")
            logger.info(f"💾 节省内存: {file_size_gb:.2f} GB")
            
            # 直接引用相同的索引对象
            PRELOADED_INDEXES[case_name] = existing_info
            total_memory_saved += file_size_gb
            
        else:
            # 首次加载此文件
            logger.info(f"📥 首次加载: {case_name}")
            
            try:
                start_time = time.time()
                
                # 加载索引
                index = faiss.read_index(index_path)
                load_time = time.time() - start_time
                
                # 创建索引信息
                index_info = {
                    "index": index,
                    "dimension": index.d,
                    "total_vectors": index.ntotal,
                    "index_type": "HNSW",
                    "file_path": real_path,
                    "load_time": load_time,
                    "file_size_gb": file_size_gb
                }
                
                PRELOADED_INDEXES[case_name] = index_info
                loaded_files[real_path] = (case_name, index_info)
                
                logger.info(f"✅ 加载成功: {case_name}")
                logger.info(f"   向量数量: {index.ntotal:,}")
                logger.info(f"   维度: {index.d}")
                logger.info(f"   加载时间: {load_time:.2f}秒")
                
            except Exception as e:
                logger.error(f"❌ 加载失败: {case_name} - {e}")
    
    # 📊 总结报告
    logger.info("🎯 预加载完成!")
    logger.info(f"   索引配置数: {len(PRELOADED_INDEXES)}")
    logger.info(f"   实际文件数: {len(loaded_files)}")
    logger.info(f"   节省内存: {total_memory_saved:.2f} GB")
    
    # 计算总内存使用
    total_memory_used = 0
    for case_name, info in PRELOADED_INDEXES.items():
        memory_gb = info['total_vectors'] * info['dimension'] * 4 / (1024**3)
        total_memory_used += memory_gb if case_name in [case for case, _ in loaded_files.values()] else 0
        
        logger.info(f"📊 {case_name}: {info['total_vectors']:,} 向量, "
                   f"文件: {info.get('file_size_gb', 0):.1f} GB")
    
    logger.info(f"💾 实际内存使用: {total_memory_used:.1f} GB")
    logger.info(f"🔑 Copy-on-Write: 所有Worker进程共享这些索引")
    
    # 🧹 强制垃圾回收
    import gc
    gc.collect()
    logger.info("🧹 垃圾回收完成")
    
    return PRELOADED_INDEXES

def get_preload_status():
    """获取预加载状态"""
    return {
        "preload_lock": _preload_lock,
        "master_pid": _master_process_pid,
        "current_pid": os.getpid(),
        "indexes_loaded": len(PRELOADED_INDEXES),
        "is_master": is_master_process()
    }

if __name__ == "__main__":
    # 测试
    logging.basicConfig(level=logging.INFO)
    index_dir = "/home/<USER>/VectorDBBench/prebuilt_indexes"
    result = preload_common_indexes_optimized(index_dir)
    print(f"预加载结果: {len(result)} 个索引")
