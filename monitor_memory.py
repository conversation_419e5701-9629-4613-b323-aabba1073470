#!/usr/bin/env python3
"""
实时监控内存使用情况
"""

import time
import psutil
import os
from datetime import datetime

def get_memory_info():
    """获取内存信息"""
    memory = psutil.virtual_memory()
    return {
        "total_gb": memory.total / (1024**3),
        "used_gb": memory.used / (1024**3),
        "available_gb": memory.available / (1024**3),
        "percent": memory.percent
    }

def get_faiss_processes():
    """获取FAISS相关进程"""
    processes = []
    for proc in psutil.process_iter(['pid', 'name', 'memory_info', 'cmdline']):
        try:
            if any('smart_faiss_server' in str(item) for item in proc.info['cmdline']):
                memory_mb = proc.info['memory_info'].rss / (1024**2)
                processes.append({
                    'pid': proc.info['pid'],
                    'memory_mb': memory_mb,
                    'cmdline': ' '.join(proc.info['cmdline'][:3])  # 前3个参数
                })
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
    return processes

def monitor_memory(interval=5, duration=300):
    """监控内存使用"""
    print(f"🔍 开始监控内存使用 (间隔: {interval}秒, 持续: {duration}秒)")
    print("=" * 80)
    
    start_time = time.time()
    max_memory = 0
    
    while time.time() - start_time < duration:
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # 系统内存
        memory = get_memory_info()
        max_memory = max(max_memory, memory['used_gb'])
        
        # FAISS进程
        faiss_processes = get_faiss_processes()
        total_faiss_memory = sum(p['memory_mb'] for p in faiss_processes)
        
        print(f"\n[{timestamp}] 内存状态:")
        print(f"  系统: {memory['used_gb']:.1f}/{memory['total_gb']:.1f} GB ({memory['percent']:.1f}%)")
        print(f"  可用: {memory['available_gb']:.1f} GB")
        print(f"  峰值: {max_memory:.1f} GB")
        
        if faiss_processes:
            print(f"  FAISS进程: {len(faiss_processes)} 个")
            print(f"  FAISS内存: {total_faiss_memory:.1f} MB ({total_faiss_memory/1024:.1f} GB)")
            for proc in faiss_processes:
                print(f"    PID {proc['pid']}: {proc['memory_mb']:.1f} MB")
        else:
            print("  FAISS进程: 未运行")
        
        # 内存警告
        if memory['percent'] > 90:
            print("  ⚠️ 内存使用率超过90%!")
        elif memory['percent'] > 80:
            print("  ⚠️ 内存使用率超过80%")
        
        time.sleep(interval)
    
    print(f"\n📊 监控完成:")
    print(f"  最大内存使用: {max_memory:.1f} GB")
    print(f"  最终内存使用: {memory['used_gb']:.1f} GB")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="监控内存使用")
    parser.add_argument("--interval", type=int, default=5, help="监控间隔(秒)")
    parser.add_argument("--duration", type=int, default=300, help="监控持续时间(秒)")
    
    args = parser.parse_args()
    
    try:
        monitor_memory(args.interval, args.duration)
    except KeyboardInterrupt:
        print("\n👋 监控已停止")
