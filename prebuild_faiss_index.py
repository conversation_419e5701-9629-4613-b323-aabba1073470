#!/usr/bin/env python3.11
"""
预构建 FAISS 索引脚本
用于提前构建索引，避免测试时的长时间等待
"""

import os
import sys
import time
import faiss
import numpy as np
import pandas as pd
from pathlib import Path

# 设置环境变量
os.environ['OMP_NUM_THREADS'] = '16'
os.environ['MKL_NUM_THREADS'] = '16'
os.environ['OPENBLAS_NUM_THREADS'] = '16'

# 数据集配置
DATASET_BASE_PATH = "/nas/yvan.chen/milvus/dataset"
INDEX_SAVE_PATH = "/home/<USER>/VectorDBBench/prebuilt_indexes"

DATASETS = {
    "Performance768D1M": {
        "path": "cohere/cohere_medium_1m",
        "dimension": 768,
        "target_count": 1000000,
        "index_file": "faiss_hnsw_768d_1m.index"
    },
    "Performance768D10M": {
        "path": "cohere/cohere_large_10m", 
        "dimension": 768,
        "target_count": 10000000,
        "index_file": "faiss_hnsw_768d_10m.index"
    }
}

def load_dataset(dataset_path: str, target_count: int, dimension: int):
    """加载数据集"""
    print(f"📁 加载数据集: {dataset_path}")
    
    full_path = Path(DATASET_BASE_PATH) / dataset_path
    if not full_path.exists():
        raise FileNotFoundError(f"数据集路径不存在: {full_path}")
    
    # 查找训练文件
    train_files = list(full_path.glob("*train*.parquet"))
    if not train_files:
        raise FileNotFoundError(f"未找到训练文件: {full_path}/*train*.parquet")
    
    print(f"📊 正在加载 {len(train_files)} 个训练文件...")
    
    all_vectors = []
    total_loaded = 0
    
    for train_file in train_files:
        print(f"📄 读取文件: {train_file.name}")
        df = pd.read_parquet(train_file)
        
        # 获取向量列
        vector_column = None
        for col in df.columns:
            if 'emb' in col.lower() or 'vector' in col.lower():
                vector_column = col
                break
        
        if vector_column is None:
            # 尝试第一个数组列
            for col in df.columns:
                if isinstance(df[col].iloc[0], (list, np.ndarray)):
                    vector_column = col
                    break
        
        if vector_column is None:
            raise ValueError(f"未找到向量列: {train_file}")
        
        # 转换为numpy数组
        vectors = np.array(df[vector_column].tolist(), dtype=np.float32)
        print(f"   向量形状: {vectors.shape}")
        
        all_vectors.append(vectors)
        total_loaded += len(vectors)
        
        print(f"📊 已加载 {total_loaded:,} 个向量")
        
        # 检查是否达到目标数量
        if total_loaded >= target_count:
            break
    
    # 合并所有向量
    if len(all_vectors) > 1:
        final_vectors = np.vstack(all_vectors)
    else:
        final_vectors = all_vectors[0]
    
    # 截取到目标数量
    if len(final_vectors) > target_count:
        final_vectors = final_vectors[:target_count]
    
    print(f"✅ 最终加载 {len(final_vectors):,} 个向量，维度: {final_vectors.shape[1]}")
    
    # 验证维度
    if final_vectors.shape[1] != dimension:
        raise ValueError(f"维度不匹配: 期望 {dimension}，实际 {final_vectors.shape[1]}")
    
    return final_vectors

def build_hnsw_index(vectors: np.ndarray, M: int = 30, ef_construction: int = 360):
    """构建 HNSW 索引"""
    dimension = vectors.shape[1]
    num_vectors = vectors.shape[0]
    
    print(f"🔧 构建 HNSW 索引...")
    print(f"   向量数量: {num_vectors:,}")
    print(f"   向量维度: {dimension}")
    print(f"   HNSW 参数: M={M}, ef_construction={ef_construction}")
    
    # 创建 HNSW 索引
    index = faiss.IndexHNSWFlat(dimension, M)
    index.hnsw.efConstruction = ef_construction
    
    # 设置多线程
    faiss.omp_set_num_threads(16)
    
    print("🚀 开始构建索引...")
    start_time = time.time()
    
    # 分批添加向量，避免内存问题
    batch_size = 50000
    for i in range(0, num_vectors, batch_size):
        end_idx = min(i + batch_size, num_vectors)
        batch = vectors[i:end_idx]
        
        print(f"   添加批次 {i//batch_size + 1}: {i:,} - {end_idx:,}")
        index.add(batch)
    
    build_time = time.time() - start_time
    print(f"✅ 索引构建完成，耗时: {build_time:.2f} 秒")
    print(f"   索引中的向量数量: {index.ntotal:,}")
    
    return index

def save_index(index, save_path: str):
    """保存索引"""
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    
    print(f"💾 保存索引到: {save_path}")
    faiss.write_index(index, save_path)
    
    # 检查文件大小
    file_size = os.path.getsize(save_path) / (1024**3)  # GB
    print(f"   文件大小: {file_size:.2f} GB")

def main():
    """主函数"""
    print("🚀 FAISS 索引预构建工具")
    print("=" * 50)
    
    # 创建保存目录
    os.makedirs(INDEX_SAVE_PATH, exist_ok=True)
    
    for dataset_name, config in DATASETS.items():
        print(f"\n📊 处理数据集: {dataset_name}")
        print("-" * 30)
        
        index_path = os.path.join(INDEX_SAVE_PATH, config["index_file"])
        
        # 检查是否已存在
        if os.path.exists(index_path):
            print(f"⚠️  索引已存在: {index_path}")
            response = input("是否重新构建? (y/N): ")
            if response.lower() != 'y':
                print("跳过...")
                continue
        
        try:
            # 加载数据集
            vectors = load_dataset(
                config["path"], 
                config["target_count"], 
                config["dimension"]
            )
            
            # 构建索引
            index = build_hnsw_index(vectors, M=30, ef_construction=360)
            
            # 保存索引
            save_index(index, index_path)
            
            print(f"✅ {dataset_name} 索引构建完成")
            
        except Exception as e:
            print(f"❌ {dataset_name} 构建失败: {e}")
            continue
    
    print("\n🎉 所有索引构建完成！")
    print(f"📁 索引保存位置: {INDEX_SAVE_PATH}")
    print("\n📋 可用索引:")
    for dataset_name, config in DATASETS.items():
        index_path = os.path.join(INDEX_SAVE_PATH, config["index_file"])
        if os.path.exists(index_path):
            file_size = os.path.getsize(index_path) / (1024**3)
            print(f"   ✅ {dataset_name}: {config['index_file']} ({file_size:.2f} GB)")
        else:
            print(f"   ❌ {dataset_name}: 未构建")

if __name__ == "__main__":
    main()
