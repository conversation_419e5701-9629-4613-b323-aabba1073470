#!/usr/bin/env python3.11
"""
快速构建 1M 数据集索引
专门用于测试，避免每次都重新加载
"""

import os
import sys
import time
import faiss
import numpy as np
import pandas as pd
from pathlib import Path

# 设置环境变量
os.environ['OMP_NUM_THREADS'] = '16'
os.environ['MKL_NUM_THREADS'] = '16'
os.environ['OPENBLAS_NUM_THREADS'] = '16'

def main():
    print("🚀 快速构建 1M 数据集索引")
    print("=" * 40)
    
    # 配置
    dataset_path = "/nas/yvan.chen/milvus/dataset/cohere/cohere_medium_1m"
    index_save_dir = "/home/<USER>/VectorDBBench/prebuilt_indexes"
    index_file = "faiss_hnsw_768d_1m.index"
    
    # 创建保存目录
    os.makedirs(index_save_dir, exist_ok=True)
    index_path = os.path.join(index_save_dir, index_file)
    
    # 检查是否已存在
    if os.path.exists(index_path):
        print(f"✅ 索引已存在: {index_path}")
        file_size = os.path.getsize(index_path) / (1024**3)
        print(f"   文件大小: {file_size:.2f} GB")
        return
    
    print(f"📁 数据集路径: {dataset_path}")
    print(f"💾 索引保存路径: {index_path}")
    
    # 检查数据集
    dataset_dir = Path(dataset_path)
    if not dataset_dir.exists():
        print(f"❌ 数据集不存在: {dataset_path}")
        return
    
    # 查找训练文件
    train_files = list(dataset_dir.glob("*train*.parquet"))
    if not train_files:
        print(f"❌ 未找到训练文件: {dataset_path}/*train*.parquet")
        return
    
    print(f"📄 找到 {len(train_files)} 个训练文件")
    
    # 加载数据
    print("🔄 加载数据...")
    start_time = time.time()
    
    all_vectors = []
    total_loaded = 0
    target_count = 1000000  # 1M
    
    for train_file in train_files:
        print(f"   读取: {train_file.name}")
        df = pd.read_parquet(train_file)
        
        # 找向量列
        vector_column = None
        for col in df.columns:
            if 'emb' in col.lower() or 'vector' in col.lower():
                vector_column = col
                break
        
        if vector_column is None:
            for col in df.columns:
                if isinstance(df[col].iloc[0], (list, np.ndarray)):
                    vector_column = col
                    break
        
        if vector_column is None:
            print(f"❌ 未找到向量列: {train_file}")
            continue
        
        # 转换向量
        vectors = np.array(df[vector_column].tolist(), dtype=np.float32)
        print(f"   向量形状: {vectors.shape}")
        
        all_vectors.append(vectors)
        total_loaded += len(vectors)
        
        if total_loaded >= target_count:
            break
    
    # 合并向量
    if len(all_vectors) > 1:
        final_vectors = np.vstack(all_vectors)
    else:
        final_vectors = all_vectors[0]
    
    # 截取到目标数量
    if len(final_vectors) > target_count:
        final_vectors = final_vectors[:target_count]
    
    load_time = time.time() - start_time
    print(f"✅ 数据加载完成: {len(final_vectors):,} 向量, 维度: {final_vectors.shape[1]}")
    print(f"   加载时间: {load_time:.2f} 秒")
    
    # 构建索引
    print("🔧 构建 HNSW 索引...")
    dimension = final_vectors.shape[1]
    
    # 创建 HNSW 索引
    index = faiss.IndexHNSWFlat(dimension, 30)  # M=30
    index.hnsw.efConstruction = 360  # ef_construction=360
    
    # 设置多线程
    faiss.omp_set_num_threads(16)
    
    build_start = time.time()
    
    # 分批添加
    batch_size = 50000
    for i in range(0, len(final_vectors), batch_size):
        end_idx = min(i + batch_size, len(final_vectors))
        batch = final_vectors[i:end_idx]
        
        print(f"   添加批次: {i:,} - {end_idx:,}")
        index.add(batch)
    
    build_time = time.time() - build_start
    print(f"✅ 索引构建完成")
    print(f"   构建时间: {build_time:.2f} 秒")
    print(f"   索引中向量数: {index.ntotal:,}")
    
    # 保存索引
    print(f"💾 保存索引...")
    faiss.write_index(index, index_path)
    
    # 检查结果
    file_size = os.path.getsize(index_path) / (1024**3)
    total_time = time.time() - start_time
    
    print(f"🎉 完成！")
    print(f"   索引文件: {index_path}")
    print(f"   文件大小: {file_size:.2f} GB")
    print(f"   总耗时: {total_time:.2f} 秒")
    print(f"   向量数量: {index.ntotal:,}")
    print(f"   向量维度: {dimension}")

if __name__ == "__main__":
    main()
