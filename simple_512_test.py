#!/usr/bin/env python3
"""
简化的512并发测试 - 直接测试修复后的等待函数
"""

import multiprocessing as mp
import time
import logging
from multiprocessing.queues import Queue

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)s: %(message)s')
log = logging.getLogger(__name__)

class ConcurrencySlotTimeoutError(Exception):
    pass

class TestRunner:
    def __init__(self, concurrency_timeout=300):
        self.concurrency_timeout = concurrency_timeout
    
    def _wait_for_queue_fill(self, q: Queue, size: int):
        """优化的队列填充等待函数 - 修复512并发问题"""
        wait_t = 0
        check_interval = 0.1  # 初始检查间隔：100ms
        max_interval = 2.0    # 最大检查间隔：2秒
        
        log.info(f"等待 {size} 个进程启动并加入队列...")
        
        while q.qsize() < size:
            current_size = q.qsize()
            
            # 动态调整检查间隔
            if current_size == 0:
                sleep_t = check_interval  # 开始时快速检查
            elif current_size < size // 2:
                sleep_t = min(0.5, check_interval * 2)  # 进程启动中，中等频率
            else:
                sleep_t = min(max_interval, check_interval * 5)  # 大部分进程已启动，降低频率
            
            wait_t += sleep_t
            
            # 超时检查
            if wait_t > self.concurrency_timeout > 0:
                log.error(f"进程启动超时: 已等待 {wait_t:.1f}s, 当前队列大小: {current_size}/{size}")
                raise ConcurrencySlotTimeoutError
            
            # 进度日志
            if int(wait_t) % 10 == 0 and wait_t > 10:  # 每10秒打印一次进度
                progress = (current_size / size) * 100
                log.info(f"进程启动进度: {current_size}/{size} ({progress:.1f}%), 已等待: {wait_t:.1f}s")
            
            time.sleep(sleep_t)
        
        log.info(f"✅ 所有 {size} 个进程已启动完成，总耗时: {wait_t:.2f}s")

def dummy_worker(q, worker_id):
    """模拟工作进程"""
    try:
        # 模拟进程启动时间
        startup_delay = 0.1 + (worker_id % 10) * 0.01  # 0.1-0.2秒的启动延迟
        time.sleep(startup_delay)
        
        # 加入队列
        q.put(1)
        
        # 模拟一些工作
        time.sleep(2)
        
    except Exception as e:
        log.error(f"Worker {worker_id} 错误: {e}")

def test_512_concurrency():
    """测试512并发"""
    print("🧪 测试512并发修复效果")
    print("=" * 50)
    
    concurrency = 512
    runner = TestRunner(concurrency_timeout=300)  # 5分钟超时
    
    try:
        with mp.Manager() as m:
            q = m.Queue()
            
            print(f"🚀 启动 {concurrency} 个进程...")
            start_time = time.time()
            
            # 启动进程
            processes = []
            for i in range(concurrency):
                p = mp.Process(target=dummy_worker, args=(q, i))
                p.start()
                processes.append(p)
            
            # 测试等待函数
            try:
                runner._wait_for_queue_fill(q, concurrency)
                wait_time = time.time() - start_time
                
                print(f"✅ 512并发测试成功!")
                print(f"   进程启动耗时: {wait_time:.2f} 秒")
                print(f"   平均每进程启动时间: {wait_time/concurrency*1000:.2f} ms")
                
                success = True
                
            except ConcurrencySlotTimeoutError:
                print(f"❌ 512并发测试超时")
                success = False
            except Exception as e:
                print(f"❌ 512并发测试失败: {e}")
                success = False
            
            # 清理进程
            print("🧹 清理进程...")
            for p in processes:
                try:
                    if p.is_alive():
                        p.terminate()
                        p.join(timeout=1)
                        if p.is_alive():
                            p.kill()
                except:
                    pass
            
            total_time = time.time() - start_time
            print(f"⏱️  总测试时间: {total_time:.2f} 秒")
            
            return success
            
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        return False

def test_comparison():
    """对比测试：原始vs优化"""
    print("\n🔄 对比测试：原始 vs 优化等待逻辑")
    print("=" * 50)
    
    def original_wait(q, size, timeout=300):
        """原始等待逻辑"""
        wait_t = 0
        while q.qsize() < size:
            sleep_t = size if size < 10 else 10  # 原始逻辑：512并发时等待10秒
            wait_t += sleep_t
            if wait_t > timeout > 0:
                raise ConcurrencySlotTimeoutError
            time.sleep(sleep_t)
    
    # 模拟队列填充过程
    with mp.Manager() as m:
        q = m.Queue()
        
        # 模拟进程逐步加入队列
        def simulate_process_startup(q, total_processes):
            """模拟进程启动过程"""
            for i in range(total_processes):
                time.sleep(0.01)  # 每10ms启动一个进程
                q.put(1)
        
        # 启动模拟进程
        import threading
        simulator = threading.Thread(target=simulate_process_startup, args=(q, 100))
        simulator.start()
        
        # 测试原始逻辑（只测试100个进程，避免等待太久）
        print("📊 原始逻辑 (100进程):")
        start_time = time.time()
        try:
            original_wait(q, 100, timeout=30)
            original_time = time.time() - start_time
            print(f"   耗时: {original_time:.2f} 秒")
        except:
            print("   超时或失败")
        
        simulator.join()
    
    # 测试优化逻辑
    with mp.Manager() as m:
        q = m.Queue()
        
        simulator = threading.Thread(target=simulate_process_startup, args=(q, 100))
        simulator.start()
        
        print("📊 优化逻辑 (100进程):")
        start_time = time.time()
        try:
            runner = TestRunner()
            runner._wait_for_queue_fill(q, 100)
            optimized_time = time.time() - start_time
            print(f"   耗时: {optimized_time:.2f} 秒")
        except:
            print("   超时或失败")
        
        simulator.join()

def main():
    """主函数"""
    print("🔧 512并发修复测试")
    print("=" * 60)
    
    # 1. 测试512并发
    success = test_512_concurrency()
    
    # 2. 对比测试
    test_comparison()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 512并发修复测试通过!")
        print("🚀 现在可以重新运行VectorDBBench的512并发测试")
    else:
        print("❌ 512并发修复测试失败")
        print("🔧 可能需要进一步调整超时参数或检查系统资源")

if __name__ == "__main__":
    main()
