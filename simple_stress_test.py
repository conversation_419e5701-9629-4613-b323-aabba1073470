#!/usr/bin/env python3
"""
简单的FAISS服务器压力测试
测试高并发连接问题的修复效果
"""

import asyncio
import aiohttp
import numpy as np
import time
import json
from concurrent.futures import ThreadPoolExecutor
import threading
from typing import List, Dict, Any

class FAISSStressTest:
    def __init__(self, base_url: str = "http://localhost:8005"):
        self.base_url = base_url
        self.session = None
        self.results = []
        self.errors = []
        self.lock = threading.Lock()
        
    async def create_session(self):
        """创建HTTP会话，优化连接池"""
        connector = aiohttp.TCPConnector(
            limit=200,  # 总连接池大小
            limit_per_host=100,  # 每个主机的连接数
            ttl_dns_cache=300,
            use_dns_cache=True,
            keepalive_timeout=30,
            enable_cleanup_closed=True
        )
        
        timeout = aiohttp.ClientTimeout(
            total=30,
            connect=10,
            sock_read=20
        )
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout
        )
    
    async def close_session(self):
        """关闭会话"""
        if self.session:
            await self.session.close()
    
    def generate_random_vector(self, dim: int = 768) -> List[float]:
        """生成随机向量"""
        return np.random.random(dim).astype(np.float32).tolist()
    
    async def single_search_request(self, session_id: int, request_id: int) -> Dict[str, Any]:
        """单个搜索请求"""
        start_time = time.time()
        
        try:
            query_vector = self.generate_random_vector()
            
            payload = {
                "query": query_vector,
                "topk": 10
            }
            
            async with self.session.post(
                f"{self.base_url}/search",
                json=payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    end_time = time.time()
                    
                    return {
                        "session_id": session_id,
                        "request_id": request_id,
                        "status": "success",
                        "response_time": end_time - start_time,
                        "status_code": response.status,
                        "result_count": len(result.get("ids", []))
                    }
                else:
                    error_text = await response.text()
                    end_time = time.time()
                    
                    return {
                        "session_id": session_id,
                        "request_id": request_id,
                        "status": "error",
                        "response_time": end_time - start_time,
                        "status_code": response.status,
                        "error": error_text
                    }
                    
        except Exception as e:
            end_time = time.time()
            return {
                "session_id": session_id,
                "request_id": request_id,
                "status": "exception",
                "response_time": end_time - start_time,
                "error": str(e)
            }
    
    async def user_session(self, session_id: int, requests_per_user: int) -> List[Dict[str, Any]]:
        """模拟单个用户会话"""
        session_results = []
        
        for request_id in range(requests_per_user):
            result = await self.single_search_request(session_id, request_id)
            session_results.append(result)
            
            # 记录结果
            with self.lock:
                if result["status"] == "success":
                    self.results.append(result)
                else:
                    self.errors.append(result)
            
            # 小延迟避免过度压力
            await asyncio.sleep(0.01)
        
        return session_results
    
    async def run_stress_test(self, concurrent_users: int = 50, requests_per_user: int = 20):
        """运行压力测试"""
        print(f"🚀 开始压力测试")
        print(f"   并发用户: {concurrent_users}")
        print(f"   每用户请求数: {requests_per_user}")
        print(f"   总请求数: {concurrent_users * requests_per_user}")
        print("=" * 50)
        
        # 创建会话
        await self.create_session()
        
        # 检查服务器状态
        try:
            async with self.session.get(f"{self.base_url}/status") as response:
                if response.status == 200:
                    status = await response.json()
                    print(f"📊 服务器状态: {status}")
                else:
                    print(f"❌ 无法获取服务器状态: {response.status}")
                    return
        except Exception as e:
            print(f"❌ 连接服务器失败: {e}")
            return
        
        # 开始测试
        start_time = time.time()
        
        # 创建并发任务
        tasks = []
        for session_id in range(concurrent_users):
            task = asyncio.create_task(
                self.user_session(session_id, requests_per_user)
            )
            tasks.append(task)
        
        # 等待所有任务完成
        print(f"⏳ 执行 {len(tasks)} 个并发会话...")
        await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 关闭会话
        await self.close_session()
        
        # 分析结果
        self.analyze_results(total_time, concurrent_users, requests_per_user)
    
    def analyze_results(self, total_time: float, concurrent_users: int, requests_per_user: int):
        """分析测试结果"""
        total_requests = concurrent_users * requests_per_user
        successful_requests = len(self.results)
        failed_requests = len(self.errors)
        
        print("\n" + "=" * 50)
        print("📊 压力测试结果分析")
        print("=" * 50)
        
        print(f"⏱️  总耗时: {total_time:.2f} 秒")
        print(f"📈 总请求数: {total_requests}")
        print(f"✅ 成功请求: {successful_requests} ({successful_requests/total_requests*100:.1f}%)")
        print(f"❌ 失败请求: {failed_requests} ({failed_requests/total_requests*100:.1f}%)")
        print(f"🚀 平均QPS: {total_requests/total_time:.2f}")
        
        if self.results:
            response_times = [r["response_time"] for r in self.results]
            avg_response_time = sum(response_times) / len(response_times)
            max_response_time = max(response_times)
            min_response_time = min(response_times)
            
            print(f"\n📊 响应时间统计:")
            print(f"   平均响应时间: {avg_response_time*1000:.2f} ms")
            print(f"   最大响应时间: {max_response_time*1000:.2f} ms")
            print(f"   最小响应时间: {min_response_time*1000:.2f} ms")
        
        if self.errors:
            print(f"\n❌ 错误分析:")
            error_types = {}
            for error in self.errors:
                error_key = error.get("error", "Unknown")[:50]
                error_types[error_key] = error_types.get(error_key, 0) + 1
            
            for error_type, count in error_types.items():
                print(f"   {error_type}: {count} 次")
        
        # 连接问题检测
        connection_errors = [e for e in self.errors if "Connection" in str(e.get("error", ""))]
        if connection_errors:
            print(f"\n🔴 连接错误: {len(connection_errors)} 次")
            print("   这表明仍有连接池或网络配置问题")
        else:
            print(f"\n🟢 无连接错误 - 网络优化生效！")

async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="FAISS服务器压力测试")
    parser.add_argument("--concurrent-users", type=int, default=50, help="并发用户数")
    parser.add_argument("--requests-per-user", type=int, default=20, help="每用户请求数")
    parser.add_argument("--url", type=str, default="http://localhost:8005", help="服务器URL")
    
    args = parser.parse_args()
    
    # 创建测试实例
    test = FAISSStressTest(base_url=args.url)
    
    # 运行测试
    await test.run_stress_test(
        concurrent_users=args.concurrent_users,
        requests_per_user=args.requests_per_user
    )

if __name__ == "__main__":
    asyncio.run(main())
