#!/bin/bash
# FAISS 配置化启动脚本 - 支持多种并发模型

echo "🚀 FAISS 配置化启动脚本"
echo "======================="

# 设置环境变量
export DATASET_LOCAL_DIR=/nas/yvan.chen/milvus/dataset
export PYTHONPATH=/home/<USER>/VectorDBBench:$PYTHONPATH

# 停止现有服务
pkill -f smart_faiss_server 2>/dev/null || true
sleep 2

# 获取CPU核数
CPU_CORES=$(nproc)
echo "🔧 系统信息: ${CPU_CORES} CPU核心"

# 显示使用方法
show_usage() {
    echo ""
    echo "📋 使用方法:"
    echo "   $0 [模式] [额外参数]"
    echo ""
    echo "🎯 预设模式:"
    echo "   balanced        - 平衡模式 (默认)"
    echo "   http-optimized  - HTTP优化模式"
    echo "   compute-optimized - 计算优化模式"
    echo "   auto           - 自动配置模式"
    echo "   custom         - 自定义模式"
    echo ""
    echo "🛠️  自定义参数示例:"
    echo "   $0 custom --workers 4 --omp-threads 4"
    echo "   $0 custom --workers 16 --omp-threads 1"
    echo "   $0 custom --omp-threads 16  # 单进程模式"
    echo ""
    echo "📊 预设模式详情:"
    echo "   balanced: 8进程×2线程 (适合大多数场景)"
    echo "   http-optimized: 16进程×1线程 (高QPS小批量)"
    echo "   compute-optimized: 4进程×4线程 (大批量搜索)"
    echo "   auto: 根据CPU核数自动选择"
}

# 解析参数
MODE=${1:-balanced}
shift

case $MODE in
    "help"|"-h"|"--help")
        show_usage
        exit 0
        ;;
    "balanced")
        ARGS="--use-gunicorn --concurrency-model balanced"
        DESCRIPTION="平衡模式 (8进程×2线程)"
        ;;
    "http-optimized")
        ARGS="--use-gunicorn --concurrency-model http-optimized"
        DESCRIPTION="HTTP优化模式 (多进程×1线程)"
        ;;
    "compute-optimized")
        ARGS="--use-gunicorn --concurrency-model compute-optimized"
        DESCRIPTION="计算优化模式 (少进程×多线程)"
        ;;
    "auto")
        ARGS="--use-gunicorn --auto-config"
        DESCRIPTION="自动配置模式"
        ;;
    "custom")
        ARGS="--use-gunicorn --concurrency-model custom $@"
        DESCRIPTION="自定义模式"
        ;;
    "single")
        ARGS="--omp-threads ${CPU_CORES}"
        DESCRIPTION="单进程模式 (${CPU_CORES}线程)"
        ;;
    *)
        echo "❌ 未知模式: $MODE"
        show_usage
        exit 1
        ;;
esac

echo ""
echo "🎯 启动模式: $DESCRIPTION"
echo "🔧 启动参数: $ARGS"

# 获取时间戳
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
LOG_FILE="faiss_${MODE}_${TIMESTAMP}.log"

echo ""
echo "🚀 启动FAISS服务器..."

# 启动服务
nohup numactl -C 0-$((CPU_CORES-1)) python3.11 smart_faiss_server.py \
    --host 0.0.0.0 \
    --port 8005 \
    $ARGS \
    > $LOG_FILE 2>&1 &

FAISS_PID=$!

echo "✅ FAISS服务启动完成"
echo "   主进程PID: $FAISS_PID"
echo "   日志文件: $LOG_FILE"

# 等待服务启动
echo ""
echo "⏳ 等待服务启动..."
for i in {1..30}; do
    if curl -s http://localhost:8005/status > /dev/null 2>&1; then
        echo "✅ FAISS服务运行正常 (耗时: ${i}秒)"
        break
    fi
    sleep 1
    if [ $i -eq 30 ]; then
        echo "⚠️  服务启动超时，请检查日志: $LOG_FILE"
    fi
done

# 显示配置信息
echo ""
echo "📊 服务配置信息:"
curl -s http://localhost:8005/status 2>/dev/null | python3 -m json.tool 2>/dev/null || echo "   无法获取状态信息"

echo ""
echo "📋 服务管理:"
echo "   服务地址: http://0.0.0.0:8005"
echo "   主进程PID: $FAISS_PID"
echo "   日志文件: $LOG_FILE"
echo ""
echo "🔧 管理命令:"
echo "   查看日志: tail -f $LOG_FILE"
echo "   查看进程: ps aux | grep smart_faiss_server"
echo "   停止服务: kill $FAISS_PID"
echo "   API测试: curl http://localhost:8005/status"
echo ""
echo "🧪 性能测试:"
echo "   基础测试: python3.11 -m vectordb_bench.cli.vectordbbench faissremote --uri http://***********:8005 --case-type Performance768D1M --index-type HNSW --m 30 --ef-construction 360 --ef-search 100 --concurrency-duration 20 --num-concurrency 4,8"

# 显示进程信息
echo ""
echo "📊 进程信息:"
sleep 2
ps aux | grep smart_faiss_server | grep -v grep | head -10 || echo "   进程信息获取失败"
