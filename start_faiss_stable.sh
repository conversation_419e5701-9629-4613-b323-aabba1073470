#!/bin/bash
# FAISS稳定启动脚本 - 解决连接问题

echo "🚀 启动稳定版FAISS服务器"
echo "========================"

# 设置资源限制
ulimit -n 1048576
ulimit -u 1048576

# 设置环境变量
export DATASET_LOCAL_DIR=/nas/yvan.chen/milvus/dataset
export PYTHONPATH=/home/<USER>/VectorDBBench:$PYTHONPATH

# 停止现有服务
pkill -f smart_faiss_server 2>/dev/null || true
sleep 3

# 获取时间戳
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

echo "🔧 环境配置:"
echo "   文件描述符限制: $(ulimit -n)"
echo "   进程限制: $(ulimit -u)"
echo "   TCP连接队列: $(cat /proc/sys/net/core/somaxconn)"

# 启动服务
echo ""
echo "🚀 启动FAISS服务 (稳定配置)..."
nohup numactl -C 0-15 python3.11 smart_faiss_server.py \
    --host 0.0.0.0 \
    --port 8005 \
    --use-gunicorn \
    --workers 8 \
    --preload \
    > faiss_stable_${TIMESTAMP}.log 2>&1 &

FAISS_PID=$!
echo "✅ FAISS服务启动完成"
echo "   主进程PID: $FAISS_PID"
echo "   日志文件: faiss_stable_${TIMESTAMP}.log"

# 等待服务启动
echo ""
echo "⏳ 等待服务启动..."
for i in {1..30}; do
    if curl -s http://localhost:8005/status > /dev/null 2>&1; then
        echo "✅ FAISS服务运行正常 (耗时: ${i}秒)"
        break
    fi
    sleep 1
done

echo ""
echo "📋 服务信息:"
echo "   服务地址: http://0.0.0.0:8005"
echo "   主进程PID: $FAISS_PID"
echo "   日志文件: faiss_stable_${TIMESTAMP}.log"
echo ""
echo "🔧 管理命令:"
echo "   查看日志: tail -f faiss_stable_${TIMESTAMP}.log"
echo "   查看进程: ps aux | grep smart_faiss_server"
echo "   停止服务: kill $FAISS_PID"
echo "   API测试: curl http://localhost:8005/status"
