#!/usr/bin/env python3
"""
单进程FAISS服务器启动脚本
避免多进程内存爆炸问题
"""

import os
import sys
import subprocess

def main():
    print("🚀 启动单进程FAISS服务器")
    print("🔧 避免多进程内存爆炸问题")
    print("=" * 50)
    
    # 强制使用单进程模式
    cmd = [
        sys.executable,
        "smart_faiss_server.py",
        "--host", "0.0.0.0",
        "--port", "8005",
        "--omp-threads", "8",  # 使用更多线程补偿单进程
        "--batch-size", "1000",
        "--base-delay", "0.01",
        "--memory-threshold", "75"
    ]
    
    print(f"📝 执行命令: {' '.join(cmd)}")
    print()
    
    # 设置环境变量，确保不使用Gunicorn
    env = os.environ.copy()
    env["FAISS_SINGLE_PROCESS"] = "1"
    
    try:
        # 直接运行，不使用nohup
        subprocess.run(cmd, env=env, check=True)
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
