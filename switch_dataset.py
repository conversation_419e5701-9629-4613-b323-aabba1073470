#!/usr/bin/env python3
"""
FAISS数据集切换工具 - 优雅地选择1M或10M数据集
"""

import requests
import sys
import argparse
import json

def switch_dataset(host="***********", port=8005, size="1M"):
    """切换数据集大小"""
    base_url = f"http://{host}:{port}"
    
    print(f"🔄 切换到 {size} 数据集...")
    
    try:
        # 使用新的切换接口
        response = requests.post(f"{base_url}/switch_dataset/{size}", timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 切换成功!")
            print(f"   数据集: {result['message']}")
            print(f"   向量数: {result['vectors']:,}")
            print(f"   维度: {result['dimension']}")
            print(f"   索引类型: {result['index_type']}")
            return True
        else:
            error_detail = response.json() if response.headers.get('content-type') == 'application/json' else response.text
            print(f"❌ 切换失败: {response.status_code}")
            print(f"   错误: {error_detail}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ 无法连接到服务器 {base_url}")
        print(f"💡 请确保服务器正在运行")
        return False
    except Exception as e:
        print(f"❌ 切换失败: {e}")
        return False

def check_current_status(host="***********", port=8005):
    """检查当前状态"""
    base_url = f"http://{host}:{port}"
    
    try:
        response = requests.get(f"{base_url}/status", timeout=10)
        if response.status_code == 200:
            status = response.json()
            vectors = status.get('vectors_count', 0)
            
            # 推断数据集大小
            if vectors == 10000000:
                size = "10M"
            elif vectors == 500000:
                size = "500K"
            elif vectors == 5000000:
                size = "5M"
            else:
                size = f"{vectors:,}"
            
            print(f"📊 当前状态:")
            print(f"   数据集大小: {size}")
            print(f"   向量数量: {vectors:,}")
            print(f"   索引类型: {status.get('index_type', 'unknown')}")
            print(f"   维度: {status.get('dimension', 0)}")
            return True
        else:
            print(f"❌ 无法获取状态: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 状态检查失败: {e}")
        return False

def list_available_datasets(host="***********", port=8005):
    """列出可用的数据集"""
    base_url = f"http://{host}:{port}"
    
    try:
        response = requests.get(f"{base_url}/info", timeout=10)
        if response.status_code == 200:
            info = response.json()
            datasets = info.get('可用数据集', {})
            
            print(f"📋 可用数据集:")
            for name, details in datasets.items():
                vectors = details.get('expected_vectors', 0)
                if vectors == 10000000:
                    size = "10M"
                elif vectors == 500000:
                    size = "500K"
                elif vectors == 5000000:
                    size = "5M"
                else:
                    size = f"{vectors:,}"
                
                print(f"   {size}: {name} ({vectors:,} 向量, {details.get('dimension', 0)}维)")
            return True
        else:
            print(f"❌ 无法获取数据集列表: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 获取数据集列表失败: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="FAISS数据集切换工具")
    parser.add_argument("--host", default="***********", help="服务器地址")
    parser.add_argument("--port", type=int, default=8005, help="服务器端口")
    parser.add_argument("--size", choices=["10M", "500K", "5M"], help="数据集大小")
    parser.add_argument("--status", action="store_true", help="显示当前状态")
    parser.add_argument("--list", action="store_true", help="列出可用数据集")
    
    args = parser.parse_args()
    
    print("🎯 FAISS数据集切换工具")
    print("=" * 40)
    
    if args.list:
        list_available_datasets(args.host, args.port)
    elif args.status:
        check_current_status(args.host, args.port)
    elif args.size:
        if switch_dataset(args.host, args.port, args.size):
            print()
            check_current_status(args.host, args.port)
    else:
        print("使用方法:")
        print("  python3 switch_dataset.py --size 10M   # 切换到10M数据集")
        print("  python3 switch_dataset.py --status     # 查看当前状态")
        print("  python3 switch_dataset.py --list       # 列出可用数据集")
        print()
        print("环境变量方法:")
        print("  export FAISS_DATASET_SIZE=10M && python3.11 -m vectordb_bench.cli.vectordbbench faissremote ...")

if __name__ == "__main__":
    main()
