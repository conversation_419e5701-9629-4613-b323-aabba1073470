#!/usr/bin/env python3
"""
测试新的HNSW参数是否生效
"""

import numpy as np
import faiss
import time
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_hnsw_parameters():
    """测试HNSW参数"""
    
    # 创建测试数据
    dimension = 768
    n_vectors = 10000
    
    logger.info(f"创建测试数据: {n_vectors} 个 {dimension} 维向量")
    np.random.seed(42)
    vectors = np.random.random((n_vectors, dimension)).astype('float32')
    
    # 测试不同的HNSW参数
    test_configs = [
        {"M": 16, "ef_construction": 200, "name": "默认参数"},
        {"M": 32, "ef_construction": 400, "name": "新的高质量参数"},
        {"M": 64, "ef_construction": 800, "name": "极高质量参数"}
    ]
    
    for config in test_configs:
        logger.info(f"\n{'='*50}")
        logger.info(f"测试配置: {config['name']}")
        logger.info(f"M={config['M']}, ef_construction={config['ef_construction']}")
        
        # 创建HNSW索引
        index = faiss.IndexHNSWFlat(dimension, config['M'])
        index.hnsw.efConstruction = config['ef_construction']
        
        # 记录构建时间
        start_time = time.time()
        index.add(vectors)
        build_time = time.time() - start_time
        
        logger.info(f"索引构建时间: {build_time:.2f} 秒")
        logger.info(f"索引大小: {index.ntotal} 个向量")
        
        # 测试搜索性能
        query_vectors = vectors[:100]  # 使用前100个向量作为查询
        k = 10
        
        # 测试不同的ef值
        ef_values = [50, 100, 200, 400]
        
        # 计算ground truth（只计算一次）
        brute_force_index = faiss.IndexFlatL2(dimension)
        brute_force_index.add(vectors)
        gt_distances, gt_indices = brute_force_index.search(query_vectors, k)
        
        for ef in ef_values:
            index.hnsw.efSearch = ef
            
            start_time = time.time()
            distances, indices = index.search(query_vectors, k)
            search_time = time.time() - start_time
            
            # 计算召回率
            recall = 0
            for i in range(len(query_vectors)):
                intersection = len(set(indices[i]) & set(gt_indices[i]))
                recall += intersection / k
            recall /= len(query_vectors)
            
            logger.info(f"  ef={ef}: 搜索时间={search_time*1000:.2f}ms, 召回率={recall:.4f}")
        
        # 显示索引统计信息
        logger.info(f"HNSW参数确认:")
        logger.info(f"  M (每层最大连接数): {config['M']}")
        logger.info(f"  ef_construction (构建时搜索宽度): {index.hnsw.efConstruction}")
        logger.info(f"  max_level (最大层数): {index.hnsw.max_level}")
        logger.info(f"  entry_point (入口点): {index.hnsw.entry_point}")
        logger.info(f"  efSearch (当前搜索宽度): {index.hnsw.efSearch}")

if __name__ == "__main__":
    test_hnsw_parameters()
