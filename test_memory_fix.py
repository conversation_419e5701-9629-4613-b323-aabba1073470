#!/usr/bin/env python3
"""
测试内存修复效果的脚本
"""

import os
import sys
import time
import psutil
import requests
import subprocess
from pathlib import Path

def get_memory_usage():
    """获取当前内存使用情况"""
    memory = psutil.virtual_memory()
    return {
        "total": memory.total / (1024**3),  # GB
        "available": memory.available / (1024**3),  # GB
        "used": memory.used / (1024**3),  # GB
        "percent": memory.percent
    }

def get_process_memory(process_name="smart_faiss_server"):
    """获取特定进程的内存使用"""
    total_memory = 0
    process_count = 0
    
    for proc in psutil.process_iter(['pid', 'name', 'memory_info']):
        try:
            if process_name in proc.info['name']:
                memory_mb = proc.info['memory_info'].rss / (1024**2)  # MB
                total_memory += memory_mb
                process_count += 1
                print(f"  PID {proc.info['pid']}: {memory_mb:.1f} MB")
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
    
    return total_memory, process_count

def check_index_files():
    """检查索引文件是否存在软链接"""
    index_dir = "/home/<USER>/VectorDBBench/prebuilt_indexes"
    
    if not os.path.exists(index_dir):
        print(f"❌ 索引目录不存在: {index_dir}")
        return False
    
    index_files = [
        "faiss_hnsw_768d_1m.index",
        "faiss_hnsw_768d_10m.index"
    ]
    
    print(f"🔍 检查索引文件:")
    for index_file in index_files:
        index_path = os.path.join(index_dir, index_file)
        if os.path.exists(index_path):
            real_path = os.path.realpath(index_path)
            is_symlink = os.path.islink(index_path)
            file_size = os.path.getsize(real_path) / (1024**3)  # GB
            
            print(f"  ✅ {index_file}")
            print(f"     路径: {index_path}")
            print(f"     实际: {real_path}")
            print(f"     软链接: {'是' if is_symlink else '否'}")
            print(f"     大小: {file_size:.2f} GB")
            
            if is_symlink and index_path != real_path:
                print(f"     ⚠️ 检测到软链接，可能导致重复加载!")
        else:
            print(f"  ❌ {index_file} 不存在")
    
    return True

def test_server_startup():
    """测试服务器启动过程的内存使用"""
    print("🚀 测试服务器启动...")
    
    # 记录启动前内存
    print("📊 启动前内存状态:")
    before_memory = get_memory_usage()
    print(f"  系统内存: {before_memory['used']:.1f}/{before_memory['total']:.1f} GB ({before_memory['percent']:.1f}%)")
    
    # 启动服务器
    print("\n🔄 启动服务器...")
    server_process = subprocess.Popen([
        sys.executable, "smart_faiss_server.py",
        "--host", "0.0.0.0",
        "--port", "8001",
        "--use-gunicorn",
        "--workers", "2",
        "--omp-threads", "2"
    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(30)  # 给足够时间预加载索引
    
    # 检查服务器状态
    try:
        response = requests.get("http://localhost:8001/health", timeout=10)
        if response.status_code == 200:
            print("✅ 服务器启动成功")
        else:
            print(f"⚠️ 服务器响应异常: {response.status_code}")
    except Exception as e:
        print(f"❌ 服务器连接失败: {e}")
        server_process.terminate()
        return False
    
    # 记录启动后内存
    print("\n📊 启动后内存状态:")
    after_memory = get_memory_usage()
    print(f"  系统内存: {after_memory['used']:.1f}/{after_memory['total']:.1f} GB ({after_memory['percent']:.1f}%)")
    
    print(f"\n📈 内存变化:")
    memory_increase = after_memory['used'] - before_memory['used']
    print(f"  增加: {memory_increase:.1f} GB")
    
    # 检查进程内存
    print(f"\n🔍 服务器进程内存:")
    total_process_memory, process_count = get_process_memory()
    print(f"  进程数: {process_count}")
    print(f"  总内存: {total_process_memory:.1f} MB ({total_process_memory/1024:.1f} GB)")
    
    # 清理
    server_process.terminate()
    time.sleep(5)
    
    return True

def test_create_index():
    """测试创建索引的内存使用"""
    print("\n🧪 测试索引创建...")
    
    # 启动服务器（简单模式）
    server_process = subprocess.Popen([
        sys.executable, "smart_faiss_server.py",
        "--host", "0.0.0.0",
        "--port", "8002"
    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    time.sleep(15)  # 等待启动
    
    try:
        # 测试创建768维索引
        print("📝 创建768维HNSW索引...")
        before_memory = get_memory_usage()
        
        response = requests.post("http://localhost:8002/create_index", json={
            "dim": 768,
            "index_type": "HNSW"
        }, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 索引创建成功: {result}")
            
            after_memory = get_memory_usage()
            memory_change = after_memory['used'] - before_memory['used']
            print(f"📊 内存变化: {memory_change:.1f} GB")
            
            # 检查是否使用了预加载索引
            if result.get("source") == "preloaded":
                print("🎯 成功使用预加载索引，避免重复加载!")
            else:
                print("⚠️ 使用了动态创建，可能存在重复加载")
        else:
            print(f"❌ 索引创建失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    finally:
        server_process.terminate()

def main():
    print("🔧 内存修复效果测试")
    print("=" * 50)
    
    # 1. 检查索引文件
    print("\n1️⃣ 检查索引文件状态")
    check_index_files()
    
    # 2. 测试服务器启动
    print("\n2️⃣ 测试服务器启动内存使用")
    if test_server_startup():
        print("✅ 启动测试完成")
    else:
        print("❌ 启动测试失败")
    
    # 3. 测试索引创建
    print("\n3️⃣ 测试索引创建内存使用")
    test_create_index()
    
    print("\n🎯 测试完成!")
    print("\n💡 预期效果:")
    print("  - 如果存在软链接，应该看到'复用索引'日志")
    print("  - 内存使用应该从1TB降低到200-300GB")
    print("  - 创建索引时应该使用预加载索引")

if __name__ == "__main__":
    main()
