#!/usr/bin/env python3
"""
测试内存修复效果 v2 - 验证主进程预加载
"""

import os
import sys
import time
import psutil
import subprocess
import signal
from pathlib import Path

def get_memory_usage():
    """获取系统内存使用"""
    memory = psutil.virtual_memory()
    return {
        "total_gb": memory.total / (1024**3),
        "used_gb": memory.used / (1024**3),
        "available_gb": memory.available / (1024**3),
        "percent": memory.percent
    }

def get_faiss_processes():
    """获取所有FAISS相关进程"""
    processes = []
    for proc in psutil.process_iter(['pid', 'ppid', 'name', 'memory_info', 'cmdline']):
        try:
            if any('smart_faiss_server' in str(item) for item in proc.info['cmdline']):
                memory_mb = proc.info['memory_info'].rss / (1024**2)
                processes.append({
                    'pid': proc.info['pid'],
                    'ppid': proc.info['ppid'],
                    'memory_mb': memory_mb,
                    'is_master': proc.info['ppid'] == 1 or 'gunicorn' in str(proc.info['cmdline'])
                })
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
    return processes

def monitor_startup_memory():
    """监控启动过程的内存使用"""
    print("🔍 监控服务器启动内存使用...")
    
    # 记录启动前内存
    before_memory = get_memory_usage()
    print(f"📊 启动前内存: {before_memory['used_gb']:.1f}/{before_memory['total_gb']:.1f} GB")
    
    # 启动服务器
    print("\n🚀 启动服务器...")
    server_process = subprocess.Popen([
        sys.executable, "smart_faiss_server.py",
        "--host", "0.0.0.0",
        "--port", "8003",
        "--use-gunicorn",
        "--workers", "4",
        "--omp-threads", "2",
        "--preload"
    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
    
    # 监控启动过程
    max_memory = before_memory['used_gb']
    startup_time = 0
    
    print("⏳ 监控启动过程...")
    while startup_time < 60:  # 最多监控60秒
        time.sleep(2)
        startup_time += 2
        
        current_memory = get_memory_usage()
        max_memory = max(max_memory, current_memory['used_gb'])
        
        faiss_processes = get_faiss_processes()
        total_faiss_memory = sum(p['memory_mb'] for p in faiss_processes)
        
        print(f"[{startup_time:2d}s] 内存: {current_memory['used_gb']:.1f} GB, "
              f"FAISS进程: {len(faiss_processes)} 个, "
              f"FAISS内存: {total_faiss_memory:.0f} MB")
        
        # 检查进程结构
        if faiss_processes:
            master_processes = [p for p in faiss_processes if p['ppid'] == 1]
            worker_processes = [p for p in faiss_processes if p['ppid'] != 1]
            
            if master_processes and worker_processes:
                print(f"   主进程: {len(master_processes)} 个, Worker: {len(worker_processes)} 个")
                break
    
    # 等待完全启动
    print("\n⏳ 等待服务器完全启动...")
    time.sleep(20)
    
    # 最终内存检查
    final_memory = get_memory_usage()
    final_processes = get_faiss_processes()
    total_final_memory = sum(p['memory_mb'] for p in final_processes)
    
    print(f"\n📊 启动完成:")
    print(f"   最终内存: {final_memory['used_gb']:.1f} GB")
    print(f"   峰值内存: {max_memory:.1f} GB")
    print(f"   内存增长: {final_memory['used_gb'] - before_memory['used_gb']:.1f} GB")
    print(f"   FAISS进程数: {len(final_processes)}")
    print(f"   FAISS总内存: {total_final_memory:.0f} MB ({total_final_memory/1024:.1f} GB)")
    
    # 分析进程结构
    print(f"\n🔍 进程结构分析:")
    for proc in final_processes:
        proc_type = "主进程" if proc['ppid'] == 1 else "Worker"
        print(f"   PID {proc['pid']} ({proc_type}): {proc['memory_mb']:.0f} MB")
    
    # 清理
    print(f"\n🧹 清理进程...")
    try:
        server_process.terminate()
        server_process.wait(timeout=10)
    except subprocess.TimeoutExpired:
        server_process.kill()
    
    # 确保所有进程都被清理
    time.sleep(3)
    remaining_processes = get_faiss_processes()
    for proc in remaining_processes:
        try:
            os.kill(proc['pid'], signal.SIGTERM)
        except ProcessLookupError:
            pass
    
    return {
        "memory_increase": final_memory['used_gb'] - before_memory['used_gb'],
        "peak_memory": max_memory,
        "faiss_memory_gb": total_final_memory / 1024,
        "process_count": len(final_processes)
    }

def check_index_files():
    """检查索引文件状态"""
    print("🔍 检查索引文件状态...")
    
    index_dir = "/home/<USER>/VectorDBBench/prebuilt_indexes"
    if not os.path.exists(index_dir):
        print(f"❌ 索引目录不存在: {index_dir}")
        return False
    
    index_files = [
        "faiss_hnsw_768d_1m.index",
        "faiss_hnsw_768d_10m.index"
    ]
    
    real_files = set()
    
    for index_file in index_files:
        index_path = os.path.join(index_dir, index_file)
        if os.path.exists(index_path):
            real_path = os.path.realpath(index_path)
            is_symlink = os.path.islink(index_path)
            file_size = os.path.getsize(real_path) / (1024**3)
            
            print(f"📁 {index_file}:")
            print(f"   路径: {index_path}")
            print(f"   实际: {real_path}")
            print(f"   软链接: {'是' if is_symlink else '否'}")
            print(f"   大小: {file_size:.2f} GB")
            
            real_files.add(real_path)
        else:
            print(f"❌ {index_file} 不存在")
    
    print(f"\n📊 文件分析:")
    print(f"   配置文件数: {len(index_files)}")
    print(f"   实际文件数: {len(real_files)}")
    
    if len(real_files) < len(index_files):
        print(f"   ✅ 检测到文件复用，预期内存节省: {(len(index_files) - len(real_files)) * 100:.0f} GB")
    
    return len(real_files) < len(index_files)

def main():
    print("🔧 内存修复效果测试 v2")
    print("=" * 60)
    
    # 1. 检查索引文件
    print("\n1️⃣ 检查索引文件状态")
    has_symlinks = check_index_files()
    
    # 2. 监控启动内存
    print("\n2️⃣ 监控启动内存使用")
    result = monitor_startup_memory()
    
    # 3. 分析结果
    print("\n3️⃣ 结果分析")
    print(f"   内存增长: {result['memory_increase']:.1f} GB")
    print(f"   峰值内存: {result['peak_memory']:.1f} GB")
    print(f"   FAISS内存: {result['faiss_memory_gb']:.1f} GB")
    print(f"   进程数量: {result['process_count']}")
    
    # 4. 评估修复效果
    print("\n4️⃣ 修复效果评估")
    if has_symlinks:
        if result['faiss_memory_gb'] < 150:  # 预期应该在100-150GB之间
            print("✅ 修复成功！内存使用在合理范围内")
        elif result['faiss_memory_gb'] < 300:
            print("⚠️ 部分修复，内存使用有所改善但仍偏高")
        else:
            print("❌ 修复失败，内存使用仍然过高")
    else:
        print("⚠️ 未检测到软链接，无法验证复用效果")
    
    print(f"\n💡 预期效果:")
    print(f"   - 有软链接时：内存应该 < 150GB")
    print(f"   - 无软链接时：内存应该 < 300GB")
    print(f"   - 实际测量：{result['faiss_memory_gb']:.1f} GB")

if __name__ == "__main__":
    main()
