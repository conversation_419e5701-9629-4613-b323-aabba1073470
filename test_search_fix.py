#!/usr/bin/env python3
"""测试搜索修复"""

import requests
import numpy as np

def test_search_fix():
    """测试搜索返回格式是否正确"""
    base_url = "http://10.1.180.72:8005"
    
    # 创建一个随机查询向量
    query_vector = np.random.random(768).tolist()
    
    print("🔍 测试搜索功能...")
    
    try:
        # 发送搜索请求
        response = requests.post(
            f"{base_url}/search",
            json={"query": query_vector, "topk": 10},
            timeout=30
        )
        response.raise_for_status()
        result = response.json()
        
        print(f"✅ 搜索成功!")
        print(f"   返回格式: {type(result)}")
        print(f"   包含字段: {list(result.keys())}")
        
        if "ids" in result:
            ids = result["ids"]
            print(f"   IDs 类型: {type(ids)}")
            print(f"   IDs 长度: {len(ids) if isinstance(ids, list) else 'N/A'}")
            print(f"   前5个IDs: {ids[:5] if isinstance(ids, list) and len(ids) > 0 else ids}")
            
            # 检查是否是整数列表
            if isinstance(ids, list) and len(ids) > 0:
                first_id = ids[0]
                print(f"   第一个ID类型: {type(first_id)}")
                print(f"   是否为整数: {isinstance(first_id, (int, np.integer))}")
                
                # 验证所有ID都是整数
                all_ints = all(isinstance(x, (int, np.integer)) for x in ids)
                print(f"   所有ID都是整数: {all_ints}")
                
                if all_ints:
                    print("🎉 搜索返回格式正确!")
                    return True
                else:
                    print("❌ 搜索返回的ID不全是整数")
                    return False
            else:
                print("❌ IDs 不是列表或为空")
                return False
        else:
            print("❌ 响应中没有 'ids' 字段")
            return False
            
    except Exception as e:
        print(f"❌ 搜索测试失败: {e}")
        return False

if __name__ == "__main__":
    test_search_fix()
