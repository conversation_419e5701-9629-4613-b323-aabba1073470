#!/usr/bin/env python3
"""
测试服务器中的HNSW参数是否正确应用
"""

import requests
import numpy as np
import json
import time
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_server_hnsw():
    """测试服务器HNSW参数"""
    
    server_url = "http://localhost:8005"
    
    # 检查服务器状态
    try:
        response = requests.get(f"{server_url}/health")
        if response.status_code != 200:
            logger.error("服务器未运行，请先启动FAISS服务器")
            return
        logger.info("✅ 服务器运行正常")
    except requests.exceptions.ConnectionError:
        logger.error("❌ 无法连接到服务器，请确保服务器在端口8005运行")
        return
    
    # 创建测试数据
    dimension = 768
    n_vectors = 1000
    
    logger.info(f"创建测试数据: {n_vectors} 个 {dimension} 维向量")
    np.random.seed(42)
    vectors = np.random.random((n_vectors, dimension)).astype('float32')
    
    # 首先创建索引
    create_index_data = {
        "dim": dimension,
        "index_type": "HNSW",
        "index_params": {
            "M": 32,
            "ef_construction": 400
        }
    }

    logger.info("创建HNSW索引...")
    response = requests.post(f"{server_url}/create_index", json=create_index_data)

    if response.status_code == 200:
        result = response.json()
        logger.info(f"✅ 索引创建成功: {result}")
    else:
        logger.error(f"❌ 索引创建失败: {response.status_code} - {response.text}")
        return

    # 准备插入数据
    insert_data = {
        "vectors": vectors.tolist()
    }

    # 插入向量
    logger.info("插入向量到服务器...")
    start_time = time.time()
    response = requests.post(f"{server_url}/insert_bulk", json=insert_data)
    insert_time = time.time() - start_time

    if response.status_code == 200:
        result = response.json()
        logger.info(f"✅ 插入成功: {result}")
        logger.info(f"插入时间: {insert_time:.2f} 秒")
    else:
        logger.error(f"❌ 插入失败: {response.status_code} - {response.text}")
        return
    
    # 测试搜索
    query_vector = vectors[0].tolist()  # 使用第一个向量作为查询

    search_data = {
        "query": query_vector,
        "topk": 10
    }

    logger.info("执行搜索测试...")
    start_time = time.time()
    response = requests.post(f"{server_url}/search", json=search_data)
    search_time = time.time() - start_time

    if response.status_code == 200:
        result = response.json()
        logger.info(f"✅ 搜索成功")
        logger.info(f"搜索时间: {search_time*1000:.2f} ms")
        logger.info(f"返回结果: ids={result.get('ids', [])[:5]}")
        logger.info(f"距离: {result.get('distances', [])[:5]}")
    else:
        logger.error(f"❌ 搜索失败: {response.status_code} - {response.text}")
        return
    
    # 获取索引信息
    logger.info("获取索引信息...")
    response = requests.get(f"{server_url}/status")

    if response.status_code == 200:
        info = response.json()
        logger.info("📊 索引信息:")
        logger.info(f"  索引类型: {info.get('index_type', 'Unknown')}")
        logger.info(f"  向量数量: {info.get('ntotal', 'Unknown')}")
        logger.info(f"  维度: {info.get('dimension', 'Unknown')}")
        logger.info(f"  状态: {info.get('status', 'Unknown')}")

        # 显示所有可用信息
        logger.info("🔧 完整状态信息:")
        for key, value in info.items():
            logger.info(f"  {key}: {value}")
    else:
        logger.error(f"❌ 获取索引信息失败: {response.status_code} - {response.text}")

    # 也尝试获取详细信息
    response = requests.get(f"{server_url}/info")
    if response.status_code == 200:
        info = response.json()
        logger.info("ℹ️ 服务器详细信息:")
        for key, value in info.items():
            logger.info(f"  {key}: {value}")

if __name__ == "__main__":
    test_server_hnsw()
