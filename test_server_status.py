#!/usr/bin/env python3
"""
测试服务器状态修复效果
"""

import requests
import time
import json

def test_server_status(host="***********", port=8005):
    """测试服务器状态"""
    base_url = f"http://{host}:{port}"
    
    print(f"🔍 测试服务器状态: {base_url}")
    
    try:
        # 1. 检查健康状态
        print("\n1️⃣ 检查健康状态...")
        health_resp = requests.get(f"{base_url}/health", timeout=10)
        if health_resp.status_code == 200:
            print("✅ 服务器健康")
        else:
            print(f"❌ 健康检查失败: {health_resp.status_code}")
            return False
        
        # 2. 获取当前状态
        print("\n2️⃣ 获取当前状态...")
        status_resp = requests.get(f"{base_url}/status", timeout=10)
        if status_resp.status_code == 200:
            status = status_resp.json()
            print("📊 服务器状态:")
            print(f"   状态: {status.get('status', 'unknown')}")
            print(f"   向量数量: {status.get('vectors_count', 0):,}")
            print(f"   总向量数: {status.get('total_vectors', 0):,}")
            print(f"   已加载向量: {status.get('vectors_loaded', 0):,}")
            print(f"   索引类型: {status.get('index_type', 'unknown')}")
            print(f"   维度: {status.get('dimension', 0)}")
            
            vectors_count = status.get('vectors_count', 0) or status.get('total_vectors', 0)
            
            if vectors_count == 0:
                print("⚠️ 检测到0个向量，可能需要创建索引")
                return test_create_index(base_url)
            else:
                print(f"✅ 检测到 {vectors_count:,} 个向量，缓存应该可用")
                return True
        else:
            print(f"❌ 状态检查失败: {status_resp.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_create_index(base_url):
    """测试创建索引"""
    print("\n3️⃣ 测试创建索引...")
    
    try:
        # 创建768维HNSW索引
        create_resp = requests.post(f"{base_url}/create_index", json={
            "dim": 768,
            "index_type": "HNSW",
            "m": 30,
            "ef_construction": 360
        }, timeout=120)
        
        if create_resp.status_code == 200:
            result = create_resp.json()
            print("✅ 索引创建成功:")
            print(f"   消息: {result.get('message', 'N/A')}")
            print(f"   向量数: {result.get('vectors', 'N/A')}")
            print(f"   维度: {result.get('dimension', 'N/A')}")
            print(f"   来源: {result.get('source', 'N/A')}")
            
            # 再次检查状态
            print("\n4️⃣ 验证索引创建后的状态...")
            time.sleep(2)
            status_resp = requests.get(f"{base_url}/status", timeout=10)
            if status_resp.status_code == 200:
                status = status_resp.json()
                vectors_count = status.get('vectors_count', 0) or status.get('total_vectors', 0)
                print(f"📊 更新后状态: {vectors_count:,} 个向量")
                
                if vectors_count > 0:
                    print("✅ 状态修复成功！")
                    return True
                else:
                    print("❌ 状态仍然显示0个向量")
                    return False
            else:
                print("❌ 无法验证状态")
                return False
        else:
            print(f"❌ 索引创建失败: {create_resp.status_code}")
            try:
                error_detail = create_resp.json()
                print(f"   错误详情: {error_detail}")
            except:
                print(f"   错误内容: {create_resp.text}")
            return False
            
    except Exception as e:
        print(f"❌ 创建索引失败: {e}")
        return False

def test_vectordbbench_compatibility():
    """测试VectorDBBench兼容性"""
    print("\n5️⃣ 测试VectorDBBench兼容性...")
    
    # 模拟VectorDBBench的检查逻辑
    host = "***********"
    port = 8005
    base_url = f"http://{host}:{port}"
    
    try:
        status_resp = requests.get(f"{base_url}/status", timeout=10)
        if status_resp.status_code == 200:
            status = status_resp.json()
            vectors_count = status.get('vectors_count', 0) or status.get('total_vectors', 0)
            server_index_type = status.get('index_type', '')
            server_dim = status.get('dimension', 0)
            
            print(f"📊 VectorDBBench视角:")
            print(f"   向量数量: {vectors_count:,}")
            print(f"   索引类型: {server_index_type}")
            print(f"   维度: {server_dim}")
            
            # 检查缓存可用性（模拟客户端逻辑）
            target_index_type = "HNSW"
            target_dim = 768
            
            if (vectors_count >= 1000 and 
                server_index_type == target_index_type and 
                server_dim == target_dim):
                
                print("✅ 缓存条件满足:")
                print(f"   ✓ 向量数量充足: {vectors_count:,} >= 1000")
                print(f"   ✓ 索引类型匹配: {server_index_type} == {target_index_type}")
                print(f"   ✓ 维度匹配: {server_dim} == {target_dim}")
                print("🚀 VectorDBBench应该会跳过数据加载")
                return True
            else:
                print("❌ 缓存条件不满足:")
                if vectors_count < 1000:
                    print(f"   ✗ 向量数量不足: {vectors_count:,} < 1000")
                if server_index_type != target_index_type:
                    print(f"   ✗ 索引类型不匹配: {server_index_type} != {target_index_type}")
                if server_dim != target_dim:
                    print(f"   ✗ 维度不匹配: {server_dim} != {target_dim}")
                print("⚠️ VectorDBBench会重新加载数据")
                return False
        else:
            print(f"❌ 状态检查失败: {status_resp.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 兼容性测试失败: {e}")
        return False

def main():
    print("🔧 服务器状态修复效果测试")
    print("=" * 50)
    
    # 测试服务器状态
    if test_server_status():
        print("\n✅ 服务器状态测试通过")
    else:
        print("\n❌ 服务器状态测试失败")
        return
    
    # 测试VectorDBBench兼容性
    if test_vectordbbench_compatibility():
        print("\n✅ VectorDBBench兼容性测试通过")
        print("\n🎯 修复成功！现在应该可以正常使用缓存了")
    else:
        print("\n❌ VectorDBBench兼容性测试失败")
        print("\n💡 可能需要进一步调试")

if __name__ == "__main__":
    main()
