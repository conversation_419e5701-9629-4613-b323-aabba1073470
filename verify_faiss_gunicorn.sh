#!/bin/bash
# FAISS Gunicorn 服务验证脚本

echo "🔍 FAISS Gunicorn 服务验证"
echo "========================="

# 1. 检查进程状态
echo "📊 检查进程状态..."
GUNICORN_PROCESSES=$(ps aux | grep gunicorn | grep -v grep)
if [ -n "$GUNICORN_PROCESSES" ]; then
    echo "✅ Gunicorn 进程运行中:"
    echo "$GUNICORN_PROCESSES"
    
    # 统计进程数量
    MASTER_COUNT=$(echo "$GUNICORN_PROCESSES" | grep "master" | wc -l)
    WORKER_COUNT=$(echo "$GUNICORN_PROCESSES" | grep "worker" | wc -l)
    
    echo ""
    echo "📈 进程统计:"
    echo "   Master 进程: $MASTER_COUNT"
    echo "   Worker 进程: $WORKER_COUNT"
    echo "   总进程数: $((MASTER_COUNT + WORKER_COUNT))"
else
    echo "❌ 未找到 Gunicorn 进程"
    exit 1
fi

# 2. 检查端口监听
echo ""
echo "🌐 检查端口监听..."
PORT_STATUS=$(netstat -tlnp 2>/dev/null | grep ":8005" || ss -tlnp | grep ":8005")
if [ -n "$PORT_STATUS" ]; then
    echo "✅ 端口 8005 正在监听:"
    echo "$PORT_STATUS"
else
    echo "❌ 端口 8005 未监听"
    exit 1
fi

# 3. 检查API响应
echo ""
echo "🔗 检查API响应..."

# 测试状态端点
echo "   测试 /status 端点..."
STATUS_RESPONSE=$(curl -s -w "%{http_code}" http://localhost:8005/status)
HTTP_CODE="${STATUS_RESPONSE: -3}"
RESPONSE_BODY="${STATUS_RESPONSE%???}"

if [ "$HTTP_CODE" = "200" ]; then
    echo "   ✅ /status 响应正常 (HTTP $HTTP_CODE)"
    echo "   响应内容: $RESPONSE_BODY"
else
    echo "   ❌ /status 响应异常 (HTTP $HTTP_CODE)"
    echo "   响应内容: $RESPONSE_BODY"
fi

# 测试信息端点
echo "   测试 /info 端点..."
INFO_RESPONSE=$(curl -s -w "%{http_code}" http://localhost:8005/info)
HTTP_CODE="${INFO_RESPONSE: -3}"
RESPONSE_BODY="${INFO_RESPONSE%???}"

if [ "$HTTP_CODE" = "200" ]; then
    echo "   ✅ /info 响应正常 (HTTP $HTTP_CODE)"
    echo "   响应内容: $RESPONSE_BODY"
else
    echo "   ❌ /info 响应异常 (HTTP $HTTP_CODE)"
fi

# 4. 检查内存使用
echo ""
echo "💾 检查内存使用..."
MEMORY_INFO=$(ps aux | grep gunicorn | grep -v grep | awk '{sum+=$6} END {printf "%.2f MB\n", sum/1024}')
echo "   总内存使用: $MEMORY_INFO"

# 详细内存信息
echo "   详细内存信息:"
ps aux | grep gunicorn | grep -v grep | awk '{printf "   PID %s: %.2f MB (%s)\n", $2, $6/1024, $11}'

# 5. 检查CPU使用
echo ""
echo "🖥️  检查CPU使用..."
CPU_INFO=$(ps aux | grep gunicorn | grep -v grep | awk '{sum+=$3} END {printf "%.2f%%\n", sum}')
echo "   总CPU使用: $CPU_INFO"

# 6. 检查日志文件
echo ""
echo "📝 检查日志文件..."
LOG_FILE="faiss_server_gunicorn.log"
if [ -f "$LOG_FILE" ]; then
    echo "✅ 日志文件存在: $LOG_FILE"
    echo "   文件大小: $(du -h $LOG_FILE | cut -f1)"
    echo "   最后10行:"
    tail -10 "$LOG_FILE" | sed 's/^/   /'
else
    echo "❌ 日志文件不存在: $LOG_FILE"
fi

# 7. 检查配置文件
echo ""
echo "⚙️  检查配置文件..."
CONFIG_FILE="gunicorn_faiss_config.py"
if [ -f "$CONFIG_FILE" ]; then
    echo "✅ 配置文件存在: $CONFIG_FILE"
    echo "   关键配置:"
    grep -E "(workers|preload_app|bind)" "$CONFIG_FILE" | sed 's/^/   /'
else
    echo "❌ 配置文件不存在: $CONFIG_FILE"
fi

# 8. 性能测试
echo ""
echo "⚡ 简单性能测试..."
echo "   发送10个并发请求测试响应时间..."

# 创建临时测试脚本
cat > temp_perf_test.py << 'EOF'
import requests
import time
import concurrent.futures
import statistics

def test_request():
    start_time = time.time()
    try:
        response = requests.get("http://localhost:8005/status", timeout=5)
        end_time = time.time()
        return end_time - start_time, response.status_code
    except Exception as e:
        return None, str(e)

# 并发测试
with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
    futures = [executor.submit(test_request) for _ in range(10)]
    results = [future.result() for future in futures]

# 分析结果
response_times = [r[0] for r in results if r[0] is not None]
status_codes = [r[1] for r in results if isinstance(r[1], int)]

if response_times:
    print(f"   成功请求: {len(response_times)}/10")
    print(f"   平均响应时间: {statistics.mean(response_times)*1000:.2f}ms")
    print(f"   最大响应时间: {max(response_times)*1000:.2f}ms")
    print(f"   最小响应时间: {min(response_times)*1000:.2f}ms")
    print(f"   状态码: {set(status_codes)}")
else:
    print("   ❌ 所有请求都失败了")
EOF

python3 temp_perf_test.py
rm -f temp_perf_test.py

# 9. 环境变量检查
echo ""
echo "🌍 检查环境变量..."
echo "   DATASET_LOCAL_DIR: ${DATASET_LOCAL_DIR:-未设置}"
echo "   OMP_NUM_THREADS: ${OMP_NUM_THREADS:-未设置}"
echo "   MKL_NUM_THREADS: ${MKL_NUM_THREADS:-未设置}"

# 10. 总结
echo ""
echo "📋 验证总结"
echo "==========="
if [ -n "$GUNICORN_PROCESSES" ] && [ "$HTTP_CODE" = "200" ]; then
    echo "✅ FAISS Gunicorn 服务运行正常"
    echo "   🚀 多进程模式: $WORKER_COUNT workers"
    echo "   💾 内存使用: $MEMORY_INFO"
    echo "   🖥️  CPU使用: $CPU_INFO"
    echo "   🌐 服务地址: http://localhost:8005"
    echo ""
    echo "🎯 准备进行性能测试:"
    echo "   ./test_faiss_gunicorn_performance.sh"
else
    echo "❌ 服务存在问题，请检查日志"
    echo "   查看日志: tail -f $LOG_FILE"
    echo "   重启服务: ./start_faiss_with_cgroup.sh"
fi
